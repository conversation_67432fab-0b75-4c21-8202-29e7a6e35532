package org.thingsboard.server.dao.deviceAuth;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.deviceAuth.DeviceUserAuth;
import org.thingsboard.server.dao.sql.deviceAuth.DeviceUserAuthMapper;
import org.thingsboard.server.dao.util.SqlDao;
import org.thingsboard.server.dao.util.imodel.query.deviceAuth.DeviceUserAuthBatchSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.deviceAuth.DeviceUserAuthPageRequest;
import org.thingsboard.server.dao.util.imodel.query.deviceAuth.DeviceUserAuthSaveRequest;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 设备用户权限关联服务实现类
 */
@Service
@SqlDao
public class DeviceUserAuthServiceImpl implements DeviceUserAuthService {

    @Autowired
    private DeviceUserAuthMapper deviceUserAuthMapper;

    @Override
    public IPage<DeviceUserAuth> findByPage(DeviceUserAuthPageRequest request) {
        return deviceUserAuthMapper.findByPage(request);
    }

    @Override
    public List<DeviceUserAuth> findByDeviceId(String deviceId, String tenantId) {
        return deviceUserAuthMapper.findByDeviceId(deviceId, tenantId);
    }

    @Override
    public List<DeviceUserAuth> findByUserId(String userId, String tenantId) {
        return deviceUserAuthMapper.findByUserId(userId, tenantId);
    }

    @Override
    public DeviceUserAuth save(DeviceUserAuthSaveRequest request, String tenantId) {
        DeviceUserAuth entity = new DeviceUserAuth();
        BeanUtils.copyProperties(request, entity);

        if (entity.getId() == null || entity.getId().isEmpty()) {
            // 新增
            entity.setId(UUID.randomUUID().toString());
            entity.setCreateTime(new Date());
            entity.setTenantId(tenantId);
            entity.setCreator(tenantId); // 实际应用中应该是当前用户ID
            deviceUserAuthMapper.insert(entity);
        } else {
            // 更新
            deviceUserAuthMapper.updateById(entity);
        }

        return entity;
    }

    @Override
    @Transactional
    public void batchSave(DeviceUserAuthBatchSaveRequest request, String tenantId) {
        // 先删除该设备的所有权限关联
        deviceUserAuthMapper.deleteByDeviceId(request.getDeviceId(), tenantId);

        // 批量新增
        if (request.getUserAuthList() != null && !request.getUserAuthList().isEmpty()) {
            List<DeviceUserAuth> entityList = new ArrayList<>();
            for (DeviceUserAuthBatchSaveRequest.UserAuthItem item : request.getUserAuthList()) {
                DeviceUserAuth entity = new DeviceUserAuth();
                entity.setId(UUID.randomUUID().toString());
                entity.setDeviceId(request.getDeviceId());
                entity.setDeviceName(request.getDeviceName());
                entity.setDeviceSerial(request.getDeviceSerial());
                entity.setUserId(item.getUserId());
                entity.setUserName(item.getUserName());
                entity.setAuthType(item.getAuthType());
                entity.setCreateTime(new Date());
                entity.setTenantId(tenantId);
                entity.setCreator(tenantId); // 实际应用中应该是当前用户ID
                entityList.add(entity);
            }

            if (!entityList.isEmpty()) {
                deviceUserAuthMapper.batchInsert(entityList);
            }
        }
    }

    @Override
    public boolean delete(String id) {
        return deviceUserAuthMapper.deleteById(id) > 0;
    }

    @Override
    public void deleteByDeviceId(String deviceId, String tenantId) {
        deviceUserAuthMapper.deleteByDeviceId(deviceId, tenantId);
    }
}
