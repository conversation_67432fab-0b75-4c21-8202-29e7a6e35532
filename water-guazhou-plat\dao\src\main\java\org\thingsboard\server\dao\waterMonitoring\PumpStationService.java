package org.thingsboard.server.dao.waterMonitoring;

import java.util.List;
import java.util.Map;

/**
 * 泵站监控服务接口
 */
public interface PumpStationService {

    // 使用现有的站点服务获取泵站列表

    /**
     * 获取泵站监控数据
     */
    Map<String, Object> getMonitorData(List<String> stationIds, String timeGranularity, Long startTime, Long endTime, String pumpType, String tenantId);

    /**
     * 获取泵站详情
     */
    List<Map<String, Object>> getStationDetail(List<String> stationIds, String tenantId);

    /**
     * 应用泵站方案
     */
    void applyScheme(String schemeId, List<String> stationIds, String tenantId);
}
