package org.thingsboard.server.dao.util.imodel.response.waterPlant;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 水厂压力信息导出VO
 */
@Data
public class WaterPlantPressureExportVO {
    /**
     * 水厂名称
     */
    private String waterPlantName;

    /**
     * 设备编号
     */
    private String deviceSerial;

    /**
     * 出厂水压(MPa)
     */
    private BigDecimal pressure;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 记录时间
     */
    private Date recordTime;

    /**
     * 备注
     */
    private String remark;
}
