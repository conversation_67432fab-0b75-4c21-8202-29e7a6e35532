package org.thingsboard.server.dao.util.imodel.query.smartManagement.settings;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartManagement.settings.InspectionPlanSetting;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

/**
 * 巡检计划配置查询参数
 */
@Getter
@Setter
public class InspectionPlanPageRequest extends AdvancedPageableQueryEntity<InspectionPlanSetting, InspectionPlanPageRequest> {

    /**
     * 巡检计划名称
     */
    private String planName;

    /**
     * 巡检类型
     */
    private String inspectionType;

    /**
     * 巡检周期
     */
    private String inspectionCycle;

    /**
     * 执行角色
     */
    private String executionRole;

    /**
     * 状态
     */
    private String status;

    /**
     * 租户ID
     */
    private String tenantId;

    // 注意：fromTime 和 toTime 字段已在父类 TimeableQueryEntity 中定义
}
