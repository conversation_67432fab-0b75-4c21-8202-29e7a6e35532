新增事件按钮组件
<template>
  <el-button
    type="success"
    :icon="Plus"
    @click="handleCreate"
  >
    新增
  </el-button>
  
  <!-- 事件表单弹窗 -->
  <EventOverviewForm
    v-model="showEventForm"
    :is-edit="false"
    @submit="handleEventSubmit"
  />
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { SLMessage } from '@/utils/Message'
import EventOverviewForm from './EventOverviewForm.vue'
import request from '@/plugins/axios'

const emit = defineEmits<{
  'created': []
}>()

const showEventForm = ref(false)

// 新建事件
const handleCreate = () => {
  showEventForm.value = true
}

// 处理事件表单提交
const handleEventSubmit = async (data: any) => {
  try {
    // 新增事件
    await request({
      url: '/api/workorder/event',
      method: 'post',
      data
    })
    SLMessage.success('事件创建成功')
    
    showEventForm.value = false
    emit('created')
  } catch (error) {
    console.error('事件创建失败:', error)
    SLMessage.error('事件创建失败')
  }
}
</script>

<style lang="scss" scoped>
</style>
