package org.thingsboard.server.dao.circuitSettings;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartManagement.settings.InspectionPlanSetting;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.settings.InspectionPlanPageRequest;

import java.util.List;
import java.util.Map;

/**
 * 巡检计划配置服务接口
 */
public interface InspectionPlanService {

    /**
     * 分页条件查询巡检计划
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<InspectionPlanSetting> findAllConditional(InspectionPlanPageRequest request);

    /**
     * 保存巡检计划
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    InspectionPlanSetting save(InspectionPlanSetting entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(InspectionPlanSetting entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 查询单个巡检计划
     *
     * @param id 唯一标识
     * @return 巡检计划
     */
    InspectionPlanSetting findById(String id);

    /**
     * 切换巡检计划状态
     *
     * @param id     主键ID
     * @param status 状态
     * @return 是否成功
     */
    boolean toggleStatus(String id, String status);

    /**
     * 获取检查表模板列表
     *
     * @return 检查表模板列表
     */
    List<Map<String, Object>> getChecklistTemplateList();

    /**
     * 获取执行角色列表
     *
     * @return 执行角色列表
     */
    List<Map<String, Object>> getExecutionRoleList();
}
