package org.thingsboard.server.dao.sql.waterMonitoring;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 泵站监控Mapper接口
 */
@Mapper
public interface PumpStationMapper {

    /**
     * 获取泵站列表
     */
    List<Map<String, Object>> getStationList(@Param("tenantId") String tenantId);

    /**
     * 获取泵站详情
     */
    List<Map<String, Object>> getStationDetail(@Param("stationIds") List<String> stationIds, @Param("tenantId") String tenantId);

    /**
     * 应用泵站方案
     */
    void applyScheme(@Param("schemeId") String schemeId, @Param("stationIds") List<String> stationIds, @Param("tenantId") String tenantId);

    /**
     * 根据名称或编码检查泵站是否存在
     */
    Integer checkPumpStationExists(@Param("name") String name, @Param("code") String code, @Param("tenantId") String tenantId);

    /**
     * 根据名称或编码查询泵站
     */
    Map<String, Object> getPumpStationByNameOrCode(@Param("name") String name, @Param("code") String code, @Param("tenantId") String tenantId);
}
