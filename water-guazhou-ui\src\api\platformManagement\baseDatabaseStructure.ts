import { request } from '@/plugins/axios'

// 获取数据库结构修复列表
export function getBaseDatabaseStructureList(params: any) {
  return request({
    url: '/api/base/database/structure/list',
    method: 'get',
    params
  })
}

// 获取数据库结构修复详情
export function getBaseDatabaseStructureDetail(id: string) {
  return request({
    url: '/api/base/database/structure/getDetail',
    method: 'get',
    params: { id }
  })
}

// 新增数据库结构修复配置
export function addBaseDatabaseStructure(data: any) {
  return request({
    url: '/api/base/database/structure/add',
    method: 'post',
    data
  })
}

// 修改数据库结构修复配置
export function editBaseDatabaseStructure(data: any) {
  return request({
    url: '/api/base/database/structure/edit',
    method: 'post',
    data
  })
}

// 删除数据库结构修复配置
export function deleteBaseDatabaseStructure(ids: string[]) {
  return request({
    url: '/api/base/database/structure/deleteIds',
    method: 'delete',
    data: ids
  })
} 