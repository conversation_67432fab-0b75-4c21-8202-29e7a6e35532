package org.thingsboard.server.controller.smartManagement.Settings;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartManagement.settings.InspectionPlanSetting;
import org.thingsboard.server.dao.circuitSettings.InspectionPlanService;
import org.thingsboard.server.utils.imodel.annotations.IStarController2;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.settings.InspectionPlanPageRequest;

import java.util.List;
import java.util.Map;

/**
 * 巡检计划配置控制器
 */
@IStarController2
@RequestMapping("/api/inspection/plan")
public class InspectionPlanController extends BaseController {

    @Autowired
    private InspectionPlanService inspectionPlanService;

    /**
     * 分页查询巡检计划列表
     */
    @GetMapping
    public IPage<InspectionPlanSetting> findAllConditional(InspectionPlanPageRequest request) throws ThingsboardException {
        // 设置租户ID
        request.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        return inspectionPlanService.findAllConditional(request);
    }

    /**
     * 根据ID查询巡检计划详情
     */
    @GetMapping("/{id}")
    public InspectionPlanSetting findById(@PathVariable String id) {
        return inspectionPlanService.findById(id);
    }

    /**
     * 新增巡检计划
     */
    @PostMapping
    public InspectionPlanSetting save(@RequestBody InspectionPlanSetting entity) throws ThingsboardException {
        // 设置租户ID
        entity.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        return inspectionPlanService.save(entity);
    }

    /**
     * 修改巡检计划
     */
    @PostMapping("/{id}")
    public boolean update(@RequestBody InspectionPlanSetting entity, @PathVariable String id) {
        entity.setId(id);
        return inspectionPlanService.update(entity);
    }

    /**
     * 删除巡检计划
     */
    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return inspectionPlanService.delete(id);
    }

    /**
     * 切换巡检计划状态
     */
    @PostMapping("/{id}/status")
    public boolean toggleStatus(@PathVariable String id, @RequestBody Map<String, String> params) {
        String status = params.get("status");
        return inspectionPlanService.toggleStatus(id, status);
    }

    /**
     * 获取检查表模板列表
     */
    @GetMapping("/checklist-template")
    public List<Map<String, Object>> getChecklistTemplateList() {
        return inspectionPlanService.getChecklistTemplateList();
    }

    /**
     * 获取执行角色列表
     */
    @GetMapping("/execution-role")
    public List<Map<String, Object>> getExecutionRoleList() {
        return inspectionPlanService.getExecutionRoleList();
    }
}
