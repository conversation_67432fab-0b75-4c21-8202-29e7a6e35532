package org.thingsboard.server.dao.util.imodel.query.video;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.VideoEntity;
import org.thingsboard.server.dao.util.imodel.query.PageableQueryEntity;

/**
 * 视频监控分页查询请求
 */
@Getter
@Setter
public class VideoMonitoringPageRequest extends PageableQueryEntity<VideoEntity> {
    /**
     * 设备编号
     */
    private String serialNumber;
    
    /**
     * 设备名称
     */
    private String name;
    
    /**
     * 标准地址
     */
    private String address;
    
    /**
     * 项目ID
     */
    private String projectId;
    
    /**
     * 经度
     */
    private String longitude;
    
    /**
     * 纬度
     */
    private String latitude;
    
    /**
     * 位置
     */
    private String location;

    private String tenantId;
}
