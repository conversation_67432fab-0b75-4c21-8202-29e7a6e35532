package org.thingsboard.server.controller.workOrder;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.workOrder.EventOverview;
import org.thingsboard.server.dao.orderWork.EventOverviewService;
import org.thingsboard.server.dao.util.imodel.query.workOrder.EventOverviewPageRequest;
import org.thingsboard.server.dao.util.imodel.response.model.IModel;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 事件总览控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/eventOverview")
@IStarController
public class EventOverviewController extends BaseController {

    @Autowired
    private EventOverviewService eventOverviewService;

    /**
     * 分页查询事件总览
     * 
     * @param model 模型
     * @param request 查询请求
     * @return 分页结果
     * @throws ThingsboardException 异常
     */
    @GetMapping
    public IPage<EventOverview> getEventOverviewPage(IModel model, EventOverviewPageRequest request) throws ThingsboardException {
        try {
            // 设置租户ID
            request.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
            
            return eventOverviewService.findEventOverviewByPage(request);
        } catch (Exception e) {
            log.error("Failed to get event overview page", e);
            throw handleException(e);
        }
    }

    /**
     * 根据ID查询事件详情
     *
     * @param id 事件ID
     * @return 事件详情
     * @throws ThingsboardException 异常
     */
    @GetMapping("/{id}")
    public EventOverview getEventOverviewById(@PathVariable String id) throws ThingsboardException {
        try {
            String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
            EventOverview eventOverview = eventOverviewService.findEventOverviewById(id, tenantId);

            if (eventOverview == null) {
                throw new IllegalArgumentException("Event not found with id: " + id);
            }

            return eventOverview;
        } catch (Exception e) {
            log.error("Failed to get event overview by id: {}", id, e);
            throw handleException(e);
        }
    }

    /**
     * 创建事件
     *
     * @param eventData 事件数据
     * @return 创建的事件
     * @throws ThingsboardException 异常
     */
    @PostMapping
    public EventOverview createEventOverview(@RequestBody Map<String, Object> eventData) throws ThingsboardException {
        try {
            String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
            UserId id = getCurrentUser().getId();
            eventData.put("organizerId", UUIDConverter.fromTimeUUID(id.getId()));

            // 验证必填字段
            String title = (String) eventData.get("title");
            String type = (String) eventData.get("type");
            String typeName = (String) eventData.get("typeName");
            String address = (String) eventData.get("address");

            if (title == null || title.trim().isEmpty()) {
                throw new IllegalArgumentException("Title cannot be empty");
            }
            if (type == null || type.trim().isEmpty()) {
                throw new IllegalArgumentException("Type cannot be empty");
            }
            if (address == null || address.trim().isEmpty()) {
                throw new IllegalArgumentException("Address cannot be empty");
            }

            // 验证坐标格式（如果提供）
            String coordinate = (String) eventData.get("coordinate");
            if (coordinate != null && !coordinate.trim().isEmpty() && !eventOverviewService.isValidCoordinate(coordinate)) {
                throw new IllegalArgumentException("Invalid coordinate format");
            }

            EventOverview eventOverview = eventOverviewService.createEventOverview(eventData, tenantId);

            log.info("Created event overview with id: {}", eventOverview.getId());
            return eventOverview;
        } catch (Exception e) {
            log.error("Failed to create event overview", e);
            throw handleException(e);
        }
    }

    /**
     * 更新事件
     *
     * @param id 事件ID
     * @param eventData 事件数据
     * @return 更新的事件
     * @throws ThingsboardException 异常
     */
    @PostMapping("/{id}")
    public EventOverview updateEventOverview(@PathVariable String id, @RequestBody Map<String, Object> eventData) throws ThingsboardException {
        try {
            String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());

            // 验证事件是否存在
            EventOverview existingEvent = eventOverviewService.findEventOverviewById(id, tenantId);
            if (existingEvent == null) {
                throw new IllegalArgumentException("Event not found with id: " + id);
            }

            // 验证必填字段
            String title = (String) eventData.get("title");
            String type = (String) eventData.get("type");
            String typeName = (String) eventData.get("typeName");
            String address = (String) eventData.get("address");

            if (title == null || title.trim().isEmpty()) {
                throw new IllegalArgumentException("Title cannot be empty");
            }
            if (type == null || type.trim().isEmpty()) {
                throw new IllegalArgumentException("Type cannot be empty");
            }
            if (address == null || address.trim().isEmpty()) {
                throw new IllegalArgumentException("Address cannot be empty");
            }

            // 验证坐标格式（如果提供）
            String coordinate = (String) eventData.get("coordinate");
            if (coordinate != null && !coordinate.trim().isEmpty() && !eventOverviewService.isValidCoordinate(coordinate)) {
                throw new IllegalArgumentException("Invalid coordinate format");
            }

            EventOverview eventOverview = eventOverviewService.updateEventOverview(id, eventData, tenantId);

            log.info("Updated event overview with id: {}", id);
            return eventOverview;
        } catch (Exception e) {
            log.error("Failed to update event overview with id: {}", id, e);
            throw handleException(e);
        }
    }

    /**
     * 删除事件
     *
     * @param id 事件ID
     * @return 删除结果
     * @throws ThingsboardException 异常
     */
    @DeleteMapping("/{id}")
    public Map<String, Object> deleteEventOverview(@PathVariable String id) throws ThingsboardException {
        try {
            String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());

            // 验证事件是否存在
            EventOverview existingEvent = eventOverviewService.findEventOverviewById(id, tenantId);
            if (existingEvent == null) {
                throw new IllegalArgumentException("Event not found with id: " + id);
            }

            boolean deleted = eventOverviewService.deleteEventOverview(id, tenantId);

            Map<String, Object> result = new HashMap<>();
            result.put("success", deleted);
            result.put("message", deleted ? "事件删除成功" : "事件删除失败");

            log.info("Deleted event overview with id: {}", id);
            return result;
        } catch (Exception e) {
            log.error("Failed to delete event overview with id: {}", id, e);
            throw handleException(e);
        }
    }

    /**
     * 批量删除事件
     *
     * @param request 包含事件ID列表的请求
     * @return 删除结果
     * @throws ThingsboardException 异常
     */
    @DeleteMapping("/batch")
    public Map<String, Object> batchDeleteEventOverview(@RequestBody Map<String, Object> request) throws ThingsboardException {
        try {
            String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());

            @SuppressWarnings("unchecked")
            List<String> ids = (List<String>) request.get("ids");

            if (ids == null || ids.isEmpty()) {
                throw new IllegalArgumentException("事件ID列表不能为空");
            }

            int successCount = 0;
            int totalCount = ids.size();
            List<String> failedIds = new ArrayList<>();

            for (String id : ids) {
                try {
                    // 验证事件是否存在
                    EventOverview existingEvent = eventOverviewService.findEventOverviewById(id, tenantId);
                    if (existingEvent == null) {
                        failedIds.add(id);
                        continue;
                    }
                    boolean deleted = eventOverviewService.deleteEventOverview(id, tenantId);
                    if (deleted) {
                        successCount++;
                    } else {
                        failedIds.add(id);
                    }
                } catch (Exception e) {
                    log.error("Failed to delete event with id: {}", id, e);
                    failedIds.add(id);
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("success", successCount > 0);
            result.put("successCount", successCount);
            result.put("totalCount", totalCount);
            result.put("failedCount", failedIds.size());
            result.put("failedIds", failedIds);

            if (successCount == totalCount) {
                result.put("message", "所有事件删除成功");
            } else if (successCount > 0) {
                result.put("message", String.format("成功删除 %d 个事件，失败 %d 个", successCount, failedIds.size()));
            } else {
                result.put("message", "所有事件删除失败");
            }

            log.info("Batch deleted events: success={}, total={}", successCount, totalCount);
            return result;
        } catch (Exception e) {
            log.error("Failed to batch delete events", e);
            throw handleException(e);
        }
    }

    /**
     * 更新事件坐标信息
     * 
     * @param id 事件ID
     * @param coordinateData 坐标数据
     * @return 更新结果
     * @throws ThingsboardException 异常
     */
    @PostMapping("/{id}/coordinate")
    public Map<String, Object> updateEventCoordinate(@PathVariable String id, @RequestBody Map<String, String> coordinateData) throws ThingsboardException {
        try {
            String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
            String coordinate = coordinateData.get("coordinate");
            String coordinateName = coordinateData.get("coordinateName");
            
            if (coordinate == null || coordinate.trim().isEmpty()) {
                throw new IllegalArgumentException("Coordinate cannot be empty");
            }
            
            if (!eventOverviewService.isValidCoordinate(coordinate)) {
                throw new IllegalArgumentException("Invalid coordinate format");
            }
            
            boolean success = eventOverviewService.updateEventCoordinate(id, coordinate, coordinateName, tenantId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("message", success ? "坐标更新成功" : "坐标更新失败");
            
            return result;
        } catch (Exception e) {
            log.error("Failed to update event coordinate for id: {}", id, e);
            throw handleException(e);
        }
    }

    /**
     * 批量更新事件坐标信息
     * 
     * @param updates 更新列表
     * @return 更新结果
     * @throws ThingsboardException 异常
     */
    @PostMapping("/coordinates/batch")
    public Map<String, Object> batchUpdateEventCoordinates(@RequestBody List<Map<String, String>> updates) throws ThingsboardException {
        try {
            String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
            
            int successCount = eventOverviewService.batchUpdateEventCoordinates(updates, tenantId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("totalCount", updates.size());
            result.put("successCount", successCount);
            result.put("failedCount", updates.size() - successCount);
            result.put("message", String.format("批量更新完成，成功：%d，失败：%d", successCount, updates.size() - successCount));
            
            return result;
        } catch (Exception e) {
            log.error("Failed to batch update event coordinates", e);
            throw handleException(e);
        }
    }

    /**
     * 获取事件统计信息
     * 
     * @param projectId 项目ID（可选）
     * @return 统计信息
     * @throws ThingsboardException 异常
     */
    @GetMapping("/statistics")
    public Map<String, Object> getEventStatistics(@RequestParam(required = false) String projectId) throws ThingsboardException {
        try {
            String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
            return eventOverviewService.getEventStatistics(tenantId, projectId);
        } catch (Exception e) {
            log.error("Failed to get event statistics", e);
            throw handleException(e);
        }
    }

    /**
     * 获取事件热点地区统计
     * 
     * @param limit 返回数量限制
     * @return 热点地区列表
     * @throws ThingsboardException 异常
     */
    @GetMapping("/hotspots")
    public List<Map<String, Object>> getEventHotspotStatistics(@RequestParam(defaultValue = "10") int limit) throws ThingsboardException {
        try {
            String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
            return eventOverviewService.getEventHotspotStatistics(tenantId, limit);
        } catch (Exception e) {
            log.error("Failed to get event hotspot statistics", e);
            throw handleException(e);
        }
    }

    /**
     * 获取事件趋势统计
     * 
     * @param days 统计天数
     * @return 趋势数据
     * @throws ThingsboardException 异常
     */
    @GetMapping("/trends")
    public List<Map<String, Object>> getEventTrendStatistics(@RequestParam(defaultValue = "30") int days) throws ThingsboardException {
        try {
            String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
            return eventOverviewService.getEventTrendStatistics(tenantId, days);
        } catch (Exception e) {
            log.error("Failed to get event trend statistics", e);
            throw handleException(e);
        }
    }

    /**
     * 获取事件状态分布统计
     * 
     * @param projectId 项目ID（可选）
     * @return 状态分布数据
     * @throws ThingsboardException 异常
     */
    @GetMapping("/status-distribution")
    public List<Map<String, Object>> getEventStatusDistribution(@RequestParam(required = false) String projectId) throws ThingsboardException {
        try {
            String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
            return eventOverviewService.getEventStatusDistribution(tenantId, projectId);
        } catch (Exception e) {
            log.error("Failed to get event status distribution", e);
            throw handleException(e);
        }
    }

    /**
     * 获取处理效率统计
     * 
     * @param days 统计天数
     * @return 效率统计数据
     * @throws ThingsboardException 异常
     */
    @GetMapping("/efficiency")
    public Map<String, Object> getProcessEfficiencyStatistics(@RequestParam(defaultValue = "30") int days) throws ThingsboardException {
        try {
            String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
            return eventOverviewService.getProcessEfficiencyStatistics(tenantId, days);
        } catch (Exception e) {
            log.error("Failed to get process efficiency statistics", e);
            throw handleException(e);
        }
    }

    /**
     * 获取指定范围内的事件
     * 
     * @param centerCoordinate 中心坐标
     * @param radiusMeters 半径（米）
     * @return 事件列表
     * @throws ThingsboardException 异常
     */
    @GetMapping("/nearby")
    public List<EventOverview> findEventsInRadius(
            @RequestParam String centerCoordinate,
            @RequestParam(defaultValue = "1000") double radiusMeters) throws ThingsboardException {
        try {
            String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
            
            if (!eventOverviewService.isValidCoordinate(centerCoordinate)) {
                throw new IllegalArgumentException("Invalid center coordinate format");
            }
            
            return eventOverviewService.findEventsInRadius(centerCoordinate, radiusMeters, tenantId);
        } catch (Exception e) {
            log.error("Failed to find events in radius", e);
            throw handleException(e);
        }
    }

    /**
     * 导出事件数据
     * 
     * @param request 查询条件
     * @return 事件列表
     * @throws ThingsboardException 异常
     */
    @PostMapping("/export")
    public List<EventOverview> exportEventOverview(@RequestBody EventOverviewPageRequest request) throws ThingsboardException {
        try {
            // 设置租户ID
            request.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
            
            return eventOverviewService.exportEventOverview(request);
        } catch (Exception e) {
            log.error("Failed to export event overview", e);
            throw handleException(e);
        }
    }

    /**
     * 获取超时事件列表
     * 
     * @param limit 返回数量限制
     * @return 超时事件列表
     * @throws ThingsboardException 异常
     */
    @GetMapping("/overdue")
    public List<EventOverview> getOverdueEvents(@RequestParam(defaultValue = "20") int limit) throws ThingsboardException {
        try {
            String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
            return eventOverviewService.getOverdueEvents(tenantId, limit);
        } catch (Exception e) {
            log.error("Failed to get overdue events", e);
            throw handleException(e);
        }
    }

    /**
     * 获取高优先级事件列表
     * 
     * @param limit 返回数量限制
     * @return 高优先级事件列表
     * @throws ThingsboardException 异常
     */
    @GetMapping("/high-priority")
    public List<EventOverview> getHighPriorityEvents(@RequestParam(defaultValue = "20") int limit) throws ThingsboardException {
        try {
            String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
            return eventOverviewService.getHighPriorityEvents(tenantId, limit);
        } catch (Exception e) {
            log.error("Failed to get high priority events", e);
            throw handleException(e);
        }
    }

    /**
     * 验证坐标格式
     * 
     * @param coordinate 坐标字符串
     * @return 验证结果
     */
    @GetMapping("/validate-coordinate")
    public Map<String, Object> validateCoordinate(@RequestParam String coordinate) {
        Map<String, Object> result = new HashMap<>();
        boolean isValid = eventOverviewService.isValidCoordinate(coordinate);
        result.put("valid", isValid);
        result.put("coordinate", coordinate);
        
        if (isValid) {
            try {
                String[] parts = coordinate.split(",");
                result.put("longitude", Double.parseDouble(parts[0].trim()));
                result.put("latitude", Double.parseDouble(parts[1].trim()));
            } catch (Exception e) {
                result.put("valid", false);
                result.put("error", "坐标解析失败");
            }
        } else {
            result.put("error", "坐标格式无效，正确格式：经度,纬度");
        }
        
        return result;
    }

    /**
     * 审核通过并派单
     *
     * @param id 事件ID
     * @return 操作结果
     * @throws ThingsboardException 异常
     */
    @PostMapping("/{id}/approve")
    public Map<String, Object> approveEvent(@PathVariable String id) throws ThingsboardException {
        try {
            String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
            eventOverviewService.approveEvent(tenantId, id);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "审核通过，已派单");
            return result;
        } catch (Exception e) {
            log.error("Failed to approve event: {}", id, e);
            throw handleException(e);
        }
    }

    /**
     * 驳回事件
     *
     * @param id 事件ID
     * @param request 驳回请求
     * @return 操作结果
     * @throws ThingsboardException 异常
     */
    @PostMapping("/{id}/reject")
    public Map<String, Object> rejectEvent(@PathVariable String id, @RequestBody Map<String, Object> request) throws ThingsboardException {
        try {
            String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
            String reason = (String) request.get("reason");
            eventOverviewService.rejectEvent(tenantId, id, reason);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "事件已驳回");
            return result;
        } catch (Exception e) {
            log.error("Failed to reject event: {}", id, e);
            throw handleException(e);
        }
    }

    /**
     * 直接完成事件
     *
     * @param id 事件ID
     * @return 操作结果
     * @throws ThingsboardException 异常
     */
    @PostMapping("/{id}/complete")
    public Map<String, Object> completeEvent(@PathVariable String id) throws ThingsboardException {
        try {
            String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
            eventOverviewService.completeEvent(tenantId, id);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "事件已完成");
            return result;
        } catch (Exception e) {
            log.error("Failed to complete event: {}", id, e);
            throw handleException(e);
        }
    }
}
