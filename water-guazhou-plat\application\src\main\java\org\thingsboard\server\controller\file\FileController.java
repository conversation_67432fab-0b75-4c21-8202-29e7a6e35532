package org.thingsboard.server.controller.file;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.fileupload.ISysFileService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.ArrayList;
import java.util.List;

/**
 * 文件上传控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/file")
@Api(tags = "文件上传")
public class FileController extends BaseController {

    @Autowired
    private ISysFileService sysFileService;

    @ApiOperation(value = "单文件上传")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    @PostMapping("/upload")
    public IstarResponse upload(@RequestParam("file") MultipartFile file) {
        try {
            String fileUrl = sysFileService.uploadFile(file);
            return IstarResponse.ok(fileUrl);
        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw new RuntimeException("文件上传失败", e);
        }
    }

    @ApiOperation(value = "批量文件上传")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    @PostMapping("/batch/upload")
    public IstarResponse batchUpload(@RequestParam("files") MultipartFile[] files) {
        try {
            List<String> fileUrls = new ArrayList<>();
            for (MultipartFile file : files) {
                String fileUrl = sysFileService.uploadFile(file);
                fileUrls.add(fileUrl);
            }
            return IstarResponse.ok(fileUrls);
        } catch (Exception e) {
            log.error("批量文件上传失败", e);
            throw new RuntimeException("批量文件上传失败", e);
        }
    }

    @ApiOperation(value = "删除文件")
    @PreAuthorize("hasAuthority('TENANT_ADMIN')")
    @DeleteMapping("/delete")
    public IstarResponse delete(@RequestParam String fileName) {
        try {
            sysFileService.deleteFile(fileName);
            return IstarResponse.ok();
        } catch (Exception e) {
            log.error("文件删除失败", e);
            return IstarResponse.error("文件删除失败");
        }
    }
}
