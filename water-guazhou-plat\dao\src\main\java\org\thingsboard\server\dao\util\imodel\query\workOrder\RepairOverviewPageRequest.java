package org.thingsboard.server.dao.util.imodel.query.workOrder;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.workOrder.EventOverview;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;
import org.thingsboard.server.dao.util.imodel.query.AwareTenantUUID;


import java.util.Date;

/**
 * 维修总览分页查询请求
 * 
 * <AUTHOR>
 */
@Getter
@Setter
public class RepairOverviewPageRequest extends AdvancedPageableQueryEntity<EventOverview, RepairOverviewPageRequest> implements AwareTenantUUID {

    /**
     * 创建时间开始（时间戳）
     */
    private Long createFromTime;

    /**
     * 创建时间结束（时间戳）
     */
    private Long createToTime;

    /**
     * 到期时间开始（时间戳）
     */
    private Long expireFromTime;

    /**
     * 到期时间结束（时间戳）
     */
    private Long expireToTime;

    /**
     * 事件状态
     */
    private String status;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 排序字段
     */
    private String orderBy = "create_time";

    /**
     * 排序方向
     */
    private String orderDirection = "DESC";

    /**
     * 获取创建时间开始（Date对象）
     */
    public Date getCreateTimeFrom() {
        return createFromTime != null ? new Date(createFromTime) : null;
    }

    /**
     * 获取创建时间结束（Date对象）
     */
    public Date getCreateTimeTo() {
        return createToTime != null ? new Date(createToTime) : null;
    }

    /**
     * 获取到期时间开始（Date对象）
     */
    public Date getExpireTimeFrom() {
        return expireFromTime != null ? new Date(expireFromTime) : null;
    }

    /**
     * 获取到期时间结束（Date对象）
     */
    public Date getExpireTimeTo() {
        return expireToTime != null ? new Date(expireToTime) : null;
    }

}
