package org.thingsboard.server.dao.util.imodel.query.workOrder;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.workOrder.EventOverview;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStatus;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;
import org.thingsboard.server.dao.util.imodel.query.AwareTenantUUID;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;

import java.util.Date;

/**
 * 维修总览分页查询请求
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class RepairOverviewPageRequest extends AdvancedPageableQueryEntity<EventOverview, RepairOverviewPageRequest> implements AwareTenantUUID {

    /**
     * 创建时间开始
     */
    private Date createTimeFrom;

    /**
     * 创建时间结束
     */
    private Date createTimeTo;

    /**
     * 到期时间开始
     */
    private Date expireTimeFrom;

    /**
     * 到期时间结束
     */
    private Date expireTimeTo;

    /**
     * 事件状态
     */
    private String status;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 排序字段
     */
    private String orderBy = "create_time";

    /**
     * 排序方向
     */
    private String orderDirection = "DESC";

    /**
     * 到期时间开始（用于接收前端时间戳参数）
     */
    private Object expireFromTime;

    /**
     * 到期时间结束（用于接收前端时间戳参数）
     */
    private Object expireToTime;

    /**
     * 设置到期时间开始
     */
    public void setExpireFromTime(String expireFromTime) {
        // 过滤空字符串，避免TimeUtils解析错误
        if (expireFromTime != null && !expireFromTime.trim().isEmpty()) {
            this.expireFromTime = expireFromTime;
        } else {
            this.expireFromTime = null;
        }
    }

    /**
     * 设置到期时间结束
     */
    public void setExpireToTime(String expireToTime) {
        // 过滤空字符串，避免TimeUtils解析错误
        if (expireToTime != null && !expireToTime.trim().isEmpty()) {
            this.expireToTime = expireToTime;
        } else {
            this.expireToTime = null;
        }
    }

    /**
     * 获取到期时间开始
     */
    public Date getExpireFromTime() {
        return getExpireFromTimeOrDefault(expireFromTime);
    }

    /**
     * 获取到期时间结束
     */
    public Date getExpireToTime() {
        return getExpireToTimeOrDefault(expireToTime);
    }

    /**
     * 转换到期开始时间
     */
    protected Date getExpireFromTimeOrDefault(Object expireFromTime) {
        if (expireFromTime == null) {
            return null;
        }
        // 检查是否为空字符串
        if (expireFromTime instanceof String && ((String) expireFromTime).trim().isEmpty()) {
            return null;
        }
        return org.thingsboard.server.dao.util.TimeUtils.defaultIfNull(expireFromTime, null);
    }

    /**
     * 转换到期结束时间
     */
    protected Date getExpireToTimeOrDefault(Object expireToTime) {
        if (expireToTime == null) {
            return null;
        }
        // 检查是否为空字符串
        if (expireToTime instanceof String && ((String) expireToTime).trim().isEmpty()) {
            return null;
        }
        return org.thingsboard.server.dao.util.TimeUtils.defaultIfNull(expireToTime, null);
    }

    /**
     * 获取单个状态枚举
     */
    public WorkOrderStatus getStatusEnum() {
        if (status == null || status.trim().isEmpty()) {
            return null;
        }

        try {
            return WorkOrderStatus.valueOf(status.trim());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    @Override
    public String valid(IStarHttpRequest request) {
        // 验证状态参数
        if (status != null && !status.trim().isEmpty()) {
            try {
                WorkOrderStatus.valueOf(status.trim());
            } catch (IllegalArgumentException e) {
                return "无效的事件状态: " + status;
            }
        }

        // 验证创建时间参数
        if (createTimeFrom != null && createTimeTo != null && createTimeFrom.after(createTimeTo)) {
            return "创建时间开始不能晚于结束时间";
        }

        // 验证到期时间参数
        if (expireTimeFrom != null && expireTimeTo != null && expireTimeFrom.after(expireTimeTo)) {
            return "到期时间开始不能晚于结束时间";
        }

        // 验证排序参数
        if (orderBy != null && !orderBy.trim().isEmpty()) {
            String[] validOrderFields = {
                "create_time", "update_time", "title", "status"
            };

            boolean validField = false;
            for (String field : validOrderFields) {
                if (field.equals(orderBy.trim())) {
                    validField = true;
                    break;
                }
            }

            if (!validField) {
                return "无效的排序字段: " + orderBy;
            }
        }

        if (orderDirection != null && !orderDirection.trim().isEmpty()) {
            String direction = orderDirection.trim().toUpperCase();
            if (!"ASC".equals(direction) && !"DESC".equals(direction)) {
                return "排序方向只能是ASC或DESC";
            }
        }

        return null;
    }

    @Override
    public void tenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String tenantId() {
        return tenantId;
    }
}
