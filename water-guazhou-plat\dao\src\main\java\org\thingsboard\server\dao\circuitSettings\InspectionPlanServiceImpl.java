package org.thingsboard.server.dao.circuitSettings;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartManagement.settings.InspectionPlanSetting;
import org.thingsboard.server.dao.sql.smartManagement.settings.InspectionPlanMapper;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.settings.InspectionPlanPageRequest;

import java.util.*;

/**
 * 巡检计划配置服务实现类
 */
@Service
public class InspectionPlanServiceImpl implements InspectionPlanService {

    @Autowired
    private InspectionPlanMapper inspectionPlanMapper;

    @Override
    public IPage<InspectionPlanSetting> findAllConditional(InspectionPlanPageRequest request) {
        return inspectionPlanMapper.findByPage(request);
    }

    @Override
    public InspectionPlanSetting save(InspectionPlanSetting entity) {
        if (entity.getId() == null || entity.getId().isEmpty()) {
            entity.setId(UUID.randomUUID().toString());
            entity.setCreateTime(new Date());
            inspectionPlanMapper.insert(entity);
        } else {
            inspectionPlanMapper.updateById(entity);
        }
        return entity;
    }

    @Override
    public boolean update(InspectionPlanSetting entity) {
        return inspectionPlanMapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return inspectionPlanMapper.deleteById(id) > 0;
    }

    @Override
    public InspectionPlanSetting findById(String id) {
        return inspectionPlanMapper.findById(id);
    }

    @Override
    public boolean toggleStatus(String id, String status) {
        InspectionPlanSetting entity = new InspectionPlanSetting();
        entity.setId(id);
        entity.setStatus(status);
        return inspectionPlanMapper.update(entity);
    }

    @Override
    public List<Map<String, Object>> getChecklistTemplateList() {
        List<Map<String, Object>> templates = new ArrayList<>();
        
        Map<String, Object> template1 = new HashMap<>();
        template1.put("id", "equipment_status");
        template1.put("name", "设备运行状态检查表");
        templates.add(template1);
        
        Map<String, Object> template2 = new HashMap<>();
        template2.put("id", "pipeline_pressure");
        template2.put("name", "管网压力检查表");
        templates.add(template2);
        
        Map<String, Object> template3 = new HashMap<>();
        template3.put("id", "pump_station");
        template3.put("name", "泵站运行检查表");
        templates.add(template3);
        
        Map<String, Object> template4 = new HashMap<>();
        template4.put("id", "water_quality");
        template4.put("name", "水质检测检查表");
        templates.add(template4);
        
        Map<String, Object> template5 = new HashMap<>();
        template5.put("id", "safety_hazard");
        template5.put("name", "安全隐患检查表");
        templates.add(template5);
        
        return templates;
    }

    @Override
    public List<Map<String, Object>> getExecutionRoleList() {
        List<Map<String, Object>> roles = new ArrayList<>();
        
        Map<String, Object> role1 = new HashMap<>();
        role1.put("id", "maintenance_equipment");
        role1.put("name", "运维组长、设备专员");
        roles.add(role1);
        
        Map<String, Object> role2 = new HashMap<>();
        role2.put("id", "inspector_equipment");
        role2.put("name", "巡检员、设备专员");
        roles.add(role2);
        
        Map<String, Object> role3 = new HashMap<>();
        role3.put("id", "other");
        role3.put("name", "其他");
        roles.add(role3);
        
        return roles;
    }
}
