//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by <PERSON>rn<PERSON>lower decompiler)
//

package org.thingsboard.server.controller.shuiwu.assets;

import com.alibaba.fastjson.JSONObject;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.VO.TreeNodeVO;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsAccountEntity;
import org.thingsboard.server.dao.shuiwu.assets.AssetsAccountService;
import org.thingsboard.server.utils.DepreciationUtil;
import org.thingsboard.server.utils.MapToJSONObject;

@RestController
@RequestMapping({"/api/assets/account"})
public class AssetsAccountController extends BaseController {
    @Autowired
    private AssetsAccountService assetsAccountService;
    @Autowired
    private DepreciationUtil depreciationUtil;

    public AssetsAccountController() {
    }

    @GetMapping({"{id}"})
    public AssetsAccountEntity getDetail(@PathVariable String id) {
        return this.assetsAccountService.getDetail(id);
    }

    @GetMapping({"page"})
    public PageData getPage(@RequestParam Map params) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(this.getTenantId().getId());
        params.put("tenantId", tenantId);
        JSONObject jsonObject = MapToJSONObject.toJsonObj(params);
        return this.assetsAccountService.getPage(jsonObject);
    }

    @PostMapping
    public AssetsAccountEntity save(@RequestBody AssetsAccountEntity assetsAccountEntity) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(this.getTenantId().getId());
        assetsAccountEntity.setTenantId(tenantId);
        this.depreciationUtil.generateDepreciation(assetsAccountEntity);
        return this.assetsAccountService.save(assetsAccountEntity);
    }

    @DeleteMapping
    public void delete(@RequestBody List<String> ids) {
        this.assetsAccountService.delete(ids);
    }

    @GetMapping({"getDeviceTypeList"})
    public List<String> getDeviceTypeList(@RequestParam(required = false) String projectId) throws ThingsboardException {
        return this.assetsAccountService.getDeviceTypeList(UUIDConverter.fromTimeUUID(this.getTenantId().getId()), projectId);
    }

    @GetMapping({"getListByDeviceType"})
    public List<AssetsAccountEntity> getListByDeviceType(@RequestParam String deviceType, @RequestParam(required = false) String projectId) throws ThingsboardException {
        return this.assetsAccountService.getListByDeviceType(deviceType, UUIDConverter.fromTimeUUID(this.getTenantId().getId()), projectId);
    }

    @GetMapping({"getListTree"})
    public List<TreeNodeVO> listTree(@RequestParam String projectId) {
        return this.assetsAccountService.getListTree(projectId);
    }

    @PostMapping({"unionPage"})
    public PageData getUnionPage(@RequestBody JSONObject params) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(this.getTenantId().getId());
        params.put("tenantId", tenantId);
        return this.assetsAccountService.getUnionPage(params);
    }
}
