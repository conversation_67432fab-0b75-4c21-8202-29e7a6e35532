package org.thingsboard.server.dao.orderWork;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.alarmV2.AlarmCenterService;
import org.thingsboard.server.dao.model.DTO.WorkOrderVisitMsgDTO;
import org.thingsboard.server.dao.model.sql.smartService.call.MsgVisitBack;
import org.thingsboard.server.dao.model.sql.statistic.GeneralTaskStatusStatistic;
import org.thingsboard.server.dao.model.sql.statistic.StatisticItem;
import org.thingsboard.server.dao.model.sql.statistic.StatisticTimeUnit;
import org.thingsboard.server.dao.model.sql.workOrder.*;
import org.thingsboard.server.dao.notify.SystemNotifyService;
import org.thingsboard.server.dao.sql.smartService.call.MsgVisitBackMapper;
import org.thingsboard.server.dao.sql.workOrder.NewlyWorkOrderCollaborationMapper;
import org.thingsboard.server.dao.sql.workOrder.NewlyWorkOrderMapper;
import org.thingsboard.server.dao.sql.workOrder.WorkOrderDetailMapper;
import org.thingsboard.server.dao.util.RedisUtil;
import org.thingsboard.server.dao.util.imodel.StringUtils;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartService.SendVisitMsgRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.WorkOrderListMsgRequest;
import org.thingsboard.server.dao.util.imodel.query.workOrder.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStage.RECEIVED;
import static org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStage.WAITED;
import static org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStatus.*;
import static org.thingsboard.server.dao.util.TimeUtils.firstDayOfYearByNow;
import static org.thingsboard.server.dao.util.TimeUtils.lastDayOfYearByNow;

@Slf4j
@Service
public class NewlyWorkOrderServiceImpl implements NewlyWorkOrderService {

    @Autowired
    private WorkOrderDetailMapper stageMapper;

    @Autowired
    private NewlyWorkOrderCollaborationMapper collaborationMapper;

    @Autowired
    private NewlyWorkOrderMapper workOrderMapper;

    @Autowired
    private AlarmCenterService alarmCenterService;

    @Autowired
    private SystemNotifyService systemNotifyService;

    @Autowired
    private EventOverviewService eventOverviewService;

    @Autowired
    private MsgVisitBackMapper msgVisitBackMapper;


    @Override
    public IPage<WorkOrder> findWorkOrderByPage(WorkOrderPageRequest request) {
        Set<WorkOrderStatus> betweenSet = request.stageBetweenSet();
        IPage<WorkOrder> pagify = QueryUtil.pagify(request,
                (offset, size) -> workOrderMapper.findByPage(offset, size, request, betweenSet),
                () -> workOrderMapper.countByPageRequest(request, betweenSet));

        return pagify;
    }

    @Override
    public boolean isAssigned(String id) {
        return workOrderMapper.isAssigned(id);
    }

    @Override
    @Transactional
    public WorkOrder save(WorkOrderSaveRequest request) {
        WorkOrder order = request.buildOrder();
        order.setSerialNo(RedisUtil.nextId(DataConstants.REDIS_KEY.WORK_ORDER, ""));
        workOrderMapper.insert(order);
        order.setSerialNo(workOrderMapper.getSerialNo(order.getId()));
        if (order.isDirectDispatch()) {
            WorkOrderDetail detail = request.process(order);
            stageMapper.insert(detail);
            workOrderMapper.updateById(order);

            // 发送通知消息
            systemNotifyService.sendNotify(
                    DataConstants.SYSTEM_NOTIFY_TYPE.NOTIFY.getValue(),
                    DataConstants.SYSTEM_NOTIFY_TOPIC.WORK_ORDER_DISPATCH,
                    detail.getProcessUserId(),
                    detail.getNextProcessUserId(),
                    order.getTenantId()
            );

        } else {
            // TODO 发送待办消息
            /*systemNotifyService.sendNotify(
                    DataConstants.SYSTEM_NOTIFY_TYPE.PENDING.getValue(),
                    DataConstants.SYSTEM_NOTIFY_TOPIC.WORK_ORDER_DISPATCH,
                    order.getUploadUserId(),
                    detail.getNextProcessUserId(),
                    order.getTenantId()
            );*/

        }
        request.setId(order.getId());
        return order;
    }

    @Override
    public boolean isStatus(String orderId, WorkOrderStatus status) {
        return workOrderMapper.isStatus(orderId, status.name());
    }

    @Override
    public boolean isStatus(String id, WorkOrderCollaborationStatus status) {
        return collaborationMapper.isStatus(id, status.name());
    }

    @Override
    public WorkOrderStatus getStatus(String orderId) {
        return WorkOrderStatus.valueOf(workOrderMapper.getStatus(orderId));
    }

    @Override
    public String getStepProcessUserId(String orderId) {
        return workOrderMapper.getStepProcessUserId(orderId);
    }

    @Override
    public String getPrevDetailProcessUserId(String orderId, int order) {
        return workOrderMapper.getPrevProcessUserId(orderId, order);
    }

    @Override
    public String getPrevDetailNextProcessUserId(String orderId, int order) {
        return workOrderMapper.getPrevNextProcessUserId(orderId, order);
    }

    @Override
    public void addStage(String id, WorkOrderStageRequest request) {
        addStage(id, request, false);
    }

    @Override
    @Transactional
    public void addStage(String id, WorkOrderStageRequest request, boolean forceOriginalProcessor) {
        WorkOrder order = workOrderMapper.selectById(id);
        WorkOrderDetail detail = request.process(order, forceOriginalProcessor);
        stageMapper.insert(detail);
        workOrderMapper.updateById(order);

        // 更新关联的业务逻辑
        WorkOrderStatus status = order.getStatus();

        // 更新关联的事件状态
        updateRelatedEventStatus(order, status);
        if (status.equalsAny(TERMINATED, CHARGEBACK, APPROVED)) {
            switch (order.getSource()) {
                case "异常监测":
                    alarmCenterService.complete(order.getId());
                    break;
            }
        } else {
            // 发送通知消息
            systemNotifyService.sendNotify(
                    DataConstants.SYSTEM_NOTIFY_TYPE.NOTIFY.getValue(),
                    DataConstants.SYSTEM_NOTIFY_TOPIC.WORK_ORDER_PENDING,
                    detail.getProcessUserId(),
                    detail.getNextProcessUserId(),
                    order.getTenantId()
            );
        }
    }

    @Override
    public WorkOrder find(String id) {
        return workOrderMapper.findById(id);
    }

    @Override
    public List<WorkOrderDetail> findStages(String id) {
        return stageMapper.getStagesByWorkOrder(id);
    }

    @Override
    @Transactional
    public void assign(String id, WorkOrderAssignRequest request) {
        WorkOrder order = workOrderMapper.selectById(id);
        order.setProcessLevel(request.getProcessLevel());
        order.setProcessLevelLabel(request.getProcessLevelLabel());
        WorkOrderDetail detail = request.process(order);
        stageMapper.insert(detail);
        workOrderMapper.updateById(order);
    }

    @Override
    @Transactional
    public void reassign(String id, WorkOrderReassignRequest request) {
        WorkOrder order = workOrderMapper.selectById(id);
        WorkOrderDetail detail = request.process(order);
        stageMapper.insert(detail);
        workOrderMapper.updateById(order);
    }

    @Override
    public WorkOrderStatisticEntity countWorkOrder(WorkOrderCountRequest request) {
        WorkOrderStatisticEntity statistic = new WorkOrderStatisticEntity();
        StatisticTimeUnit timeUnit = request.timeUnit();

        if (request.isCountOrganizer()) {
            StatisticItem organizerResult = QueryUtil.statistic(timeUnit, request.byTime(), request.getFromTime(), request.getToTime(),
                    (from, to) -> workOrderMapper.countByOrganizerTimed(from, to, request.tenantId()));
            statistic.setOrganizer(organizerResult);
        }

        if (request.isCountType()) {
            StatisticItem typeResult = QueryUtil.statistic(timeUnit, request.byTime(), request.getFromTime(), request.getToTime(),
                    (from, to) -> workOrderMapper.countByTypeTimed(from, to, request.tenantId()));
            statistic.setTypes(typeResult);
        }

        return statistic;
    }

    @Override
    @Transactional
    public void collaborate(String id, WorkOrderCollaborateRequest request, String tenantId) {
        // 获取来源工单
        WorkOrder fromWorkOrder = workOrderMapper.selectById(id);

        WorkOrderCollaborateRequest.ReturnWrapper returnWrapper = request.build(fromWorkOrder, tenantId);

        // 来源工单 记录 协作信息
        WorkOrderDetail detail = returnWrapper.getWorkOrderDetail();
        stageMapper.insert(detail);

        // 记录协作申请
        WorkOrderCollaboration collaboration = returnWrapper.getWorkOrderCollaboration();
        collaborationMapper.insert(collaboration);

        // 直接派发
        if (request.validateProcessLevel()) {
            // 派发新工单
            WorkOrder subWorkOrder = returnWrapper.getSubWorkOrder();
            workOrderMapper.insert(subWorkOrder);

            // 新工单的接收信息
            WorkOrderDetail stage = returnWrapper.getSubWorkStage();
            stage.setMainId(subWorkOrder.getId());
            stageMapper.insert(stage);
        }
    }

    @Override
    @Transactional
    public void collaborateVerify(String id, WorkOrderCollaborateVerifyRequest request, String tenantId) {
        // 获取协作申请信息
        WorkOrderCollaboration collaboration = collaborationMapper.selectById(id);

        if (!request.approved()) {
            // 拒绝协作申请
            collaborationMapper.setCollaborationStatus(id, WorkOrderCollaborationStatus.REJECTED.name());
            return;
        }

        // 创建新工单并获取接收细节
        WorkOrder workOrder = new WorkOrder();
        WorkOrderDetail stage = request.process(workOrder, collaboration, new Date(), tenantId);
        workOrderMapper.insert(workOrder);
        stage.setMainId(workOrder.getId());
        stageMapper.insert(stage);

        collaborationMapper.setCollaborationStatus(id, WorkOrderCollaborationStatus.APPROVED.name());
        // 发送待办消息
        systemNotifyService.sendNotify(
                DataConstants.SYSTEM_NOTIFY_TYPE.PENDING.getValue(),
                DataConstants.SYSTEM_NOTIFY_TOPIC.WORK_ORDER_PENDING,
                stage.getProcessUserId(),
                stage.getNextProcessUserId(),
                workOrder.getTenantId()
        );
    }

    @Override
    public IPage<WorkOrderCollaboration> collaborations(WorkOrderCollaborationPageRequest request) {
        return collaborationMapper.findByPage(request);
    }

    @Override
    public List<WorkOrder> children(String id) {
        return workOrderMapper.children(id);
    }


    @Override
    public boolean isUserProcess(String id, String currentUserUUID) {
        return workOrderMapper.isUserProcess(id, currentUserUUID);
    }

    @Override
    public boolean collaborIsUserProcess(String id, String currentUserUUID) {
        return collaborationMapper.isUserProcess(id, currentUserUUID);
    }

    @Override
    public Object faultCount(int page, int size, String level, String source, Long startTime, Long endTime, String tenantId) {
        Date start = null;
        Date end = null;
        if (startTime != null) {
            start = new Date(startTime);
        }

        if (endTime != null) {
            end = new Date(endTime);
        }

        List list = workOrderMapper.faultCount(page, size, level, source, start, end, tenantId);
        int total = workOrderMapper.faultCountCount(level, source, start, end, tenantId);
        PageData pageData = new PageData(total, list);

        return pageData;
    }

    @Override
    public WorkOrderCompleteCountResponse countWorkOrderComplete(WorkOrderCompleteCountRequest request) {
        Date inputFromTime = request.getFromTime();
        Date inputToTime = request.getToTime();
        return new WorkOrderCompleteCountResponse(
                workOrderMapper.countCompleteBetweenTime(request.changeTimeRange(firstDayOfYearByNow(), lastDayOfYearByNow())),
                workOrderMapper.countCompleteBetweenTime(request.changeTimeRange(inputFromTime, inputToTime)),
                QueryUtil.statisticByTimeThenKey(inputFromTime, inputToTime,
                        (from, to) -> workOrderMapper.countByTypeTimedLimitedByProcessingUser(request.changeTimeRange(from, to)))
        );
    }

    @Override
    @SuppressWarnings("rawtypes")
    public Object workTimeCount(int page, int size, String source, Long startTime, Long endTime, String tenantId) {
        Date start = null;
        Date end = null;
        if (startTime != null) {
            start = new Date(startTime);
        }

        if (endTime != null) {
            end = new Date(endTime);
        }

        List list = workOrderMapper.workTimeCount(page, size, source, start, end, tenantId);
        int total = workOrderMapper.workTimeCountCount(source, start, end, tenantId);
        PageData pageData = new PageData(total, list);

        return pageData;
    }

    @Override
    public WorkOrderCountOfStatusResponse countOfStatus(MutableWorkOrderCountOfStatusRequest req) {
        return new WorkOrderCountOfStatusResponse(
                workOrderMapper.countBetweenStage(req.loadSet(WAITED)),
                workOrderMapper.countBetweenStage(req.loadSet(RECEIVED))
        );
    }


    @Override
    @Transactional
    public boolean chargeback(String id, WorkOrderStageRequest request) {
        WorkOrderStatus stage = request.stage();
        if (stage.equals(REJECTED)) {
            request.setNextProcessUserId(workOrderMapper.getPrevProcessUserId(id, 2));
            request.setStage(workOrderMapper.getPrevProcessUseStage(id, 2));
        }
        addStage(id, request, stage.equals(CHARGEBACK));
        return true;
    }

    @Override
    public boolean chargebackRequest(String id, WorkOrderStageRequest request) {
        request.setStage(CHARGEBACK_REVIEW.name());
        if (StringUtils.isNullOrEmpty(request.getNextProcessUserId())) {
            // 默认设置为首次分派的人来进行分派
            request.setNextProcessUserId(workOrderMapper.getAssignProcessUser(id));
        }
        addStage(id, request);
        return true;
    }

    @Override
    public GeneralTaskStatusStatistic countStatusByUser(String userId, Set<WorkOrderStatus> status) {
        return new GeneralTaskStatusStatistic(
                workOrderMapper.totalStatusOfUser(userId, status),
                workOrderMapper.totalOfUser(userId)
        );
    }

    @Override
    public boolean canSubmit(String id) {
        return workOrderMapper.canSubmit(id);
    }

    @Override
    public IPage<WorkOrderVisitMsgDTO> getVisitList(WorkOrderListMsgRequest request) {
        Page<WorkOrderVisitMsgDTO> page = new Page<>(request.getPage(), request.getSize());
        page = workOrderMapper.getVisitList(page, request);
        return page;
    }

    @Override
    public List<WorkOrderVisitMsgDTO> getWorkOrderMsgList(SendVisitMsgRequest sendVisitMsgRequest) {

        List<WorkOrderVisitMsgDTO> workOrderMsgList = sendVisitMsgRequest.getWorkOrderMsgList();
        if (workOrderMsgList == null || workOrderMsgList.size() == 0) {
            WorkOrderListMsgRequest workOrderListRequest = sendVisitMsgRequest.getWorkOrderListMsgRequest();
            workOrderListRequest.setPage(1);
            workOrderListRequest.setSize(Integer.MAX_VALUE);
            IPage<WorkOrderVisitMsgDTO> list = this.getVisitList(sendVisitMsgRequest.getWorkOrderListMsgRequest());
            workOrderMsgList = list.getRecords();
        }

        if (workOrderMsgList == null || workOrderMsgList.size() == 0) {
            return new ArrayList<>();
        }

        // 导出全部
        if ("1".equals(sendVisitMsgRequest.getExportType())) {
            return workOrderMsgList;
        }

        // 筛选未发送和发送失败的
        List<String> workOrderCodeList = workOrderMsgList.stream().map(a -> a.getId()).collect(Collectors.toList());
        List<String> notSendList = this.getNotSendList(workOrderCodeList);
        workOrderMsgList = workOrderMsgList.stream().filter(a -> notSendList.contains(a.getId())).collect(Collectors.toList());
        return workOrderMsgList;
    }

    @Override
    public void updateById(WorkOrder workOrder) {
        workOrderMapper.updateById(workOrder);
    }

    public List<String> getNotSendList(List<String> workOrderCodeList) {
        QueryWrapper<MsgVisitBack> msgVisitBackQueryWrapper = new QueryWrapper<>();
        msgVisitBackQueryWrapper.in("work_order_id", workOrderCodeList);
        msgVisitBackQueryWrapper.eq("send_status", "1");

        List<MsgVisitBack> msgVisitBacks = msgVisitBackMapper.selectList(msgVisitBackQueryWrapper);
        List<String> sendIdList = msgVisitBacks.stream().map(a -> a.getWorkOrderId()).collect(Collectors.toList());

        workOrderCodeList = workOrderCodeList.stream().filter(a -> !sendIdList.contains(a)).collect(Collectors.toList());

        return workOrderCodeList;
    }

    /**
     * 更新关联事件的状态
     *
     * @param workOrder 工单信息
     * @param status 工单状态
     */
    private void updateRelatedEventStatus(WorkOrder workOrder, WorkOrderStatus status) {
        try {
            // 检查工单来源是否为"事件转工单"
//            if (!"事件转工单".equals(workOrder.getSource())) {
//                return;
//            }

            // 根据工单状态更新事件状态
            EventStatusEnums eventStatus = null;
            switch (status) {
                case RESOLVING:
                    // 工单开始处理时，事件状态改为"处理中"
                    eventStatus = EventStatusEnums.PROCESSING;
                    break;
                case APPROVED:
                    // 工单完成时，事件状态改为"已完成"
                    eventStatus = EventStatusEnums.COMPLETED;
                    break;
                case TERMINATED:
                case CHARGEBACK:
                    // 工单终止或退单时，事件状态改为"已撤回"
                    eventStatus = EventStatusEnums.REJECTED;
                    break;
                default:
                    // 其他状态不更新事件状态
                    return;
            }

            // 通过工单ID查找关联的事件并更新状态
            eventOverviewService.updateEventStatusByWorkOrderId(workOrder.getId(), eventStatus, workOrder.getTenantId());

        } catch (Exception e) {
            // 记录错误但不影响工单流程
            log.error("更新关联事件状态失败: workOrderId={}, status={}", workOrder.getId(), status, e);
        }
    }
}
