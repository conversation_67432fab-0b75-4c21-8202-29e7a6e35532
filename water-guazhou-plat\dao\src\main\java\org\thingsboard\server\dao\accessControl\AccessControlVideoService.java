package org.thingsboard.server.dao.accessControl;

import org.thingsboard.server.dao.model.sql.AccessControlVideo;
import org.thingsboard.server.dao.model.sql.VideoEntity;

import java.util.List;

/**
 * 门禁视频关联服务接口
 * 
 * <AUTHOR>
 * @date 2024
 */
public interface AccessControlVideoService {

    /**
     * 保存门禁视频关联
     * 
     * @param entity 关联实体
     * @return 保存后的实体
     */
    AccessControlVideo save(AccessControlVideo entity);

    /**
     * 批量保存门禁视频关联
     * 这是核心方法，会先删除原有关联，再保存新关联
     * 
     * @param accessControlId 门禁ID
     * @param videoIds 视频ID列表
     * @param tenantId 租户ID
     * @return 保存的关联列表
     */
    List<AccessControlVideo> batchSave(String accessControlId, List<String> videoIds, String tenantId);

    /**
     * 根据门禁ID查询关联的视频列表
     * 
     * @param accessControlId 门禁ID
     * @return 视频列表
     */
    List<VideoEntity> findVideosByAccessControlId(String accessControlId);

    /**
     * 根据视频ID查询关联的门禁列表
     * 
     * @param videoId 视频ID
     * @param tenantId 租户ID
     * @return 关联列表
     */
    List<AccessControlVideo> findByVideoId(String videoId, String tenantId);

    /**
     * 根据门禁ID查询关联信息
     * 
     * @param accessControlId 门禁ID
     * @param tenantId 租户ID
     * @return 关联列表
     */
    List<AccessControlVideo> findByAccessControlId(String accessControlId, String tenantId);

    /**
     * 删除门禁视频关联
     * 
     * @param id 关联ID
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 根据门禁ID删除所有关联
     * 
     * @param accessControlId 门禁ID
     * @return 是否删除成功
     */
    boolean deleteByAccessControlId(String accessControlId);

    /**
     * 根据视频ID删除所有关联
     * 
     * @param videoId 视频ID
     * @return 是否删除成功
     */
    boolean deleteByVideoId(String videoId);

    /**
     * 检查门禁和视频是否已关联
     * 
     * @param accessControlId 门禁ID
     * @param videoId 视频ID
     * @return 是否已关联
     */
    boolean isAssociated(String accessControlId, String videoId);

    /**
     * 获取门禁关联的视频数量
     * 
     * @param accessControlId 门禁ID
     * @return 关联的视频数量
     */
    int getVideoCountByAccessControlId(String accessControlId);

    /**
     * 获取视频关联的门禁数量
     * 
     * @param videoId 视频ID
     * @return 关联的门禁数量
     */
    int getAccessControlCountByVideoId(String videoId);
}
