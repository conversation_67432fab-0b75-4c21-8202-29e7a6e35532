package org.thingsboard.server.dao.orderWork;

import com.alibaba.fastjson.JSONObject;

import java.util.List;
import java.util.Map;

/**
 * 维修总览服务接口
 */
public interface RepairOverviewService {

    /**
     * 获取维修总览统计数据
     * @param params 查询参数
     * @return 统计数据
     */
    Map<String, Object> getStatistics(JSONObject params);

    /**
     * 获取维修记录分页列表
     * @param params 查询参数
     * @return 分页数据
     */
    Map<String, Object> getRecords(JSONObject params);

    /**
     * 导出维修总览数据
     * @param params 导出参数
     * @return 导出结果
     */
    String exportData(JSONObject params);

    /**
     * 获取维修类型选项
     * @param tenantId 租户ID
     * @return 类型列表
     */
    List<Map<String, Object>> getRepairTypes(String tenantId);

    /**
     * 获取维修状态选项
     * @return 状态列表
     */
    List<Map<String, Object>> getRepairStatuses();
}
