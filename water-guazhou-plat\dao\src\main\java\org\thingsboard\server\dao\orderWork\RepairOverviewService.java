package org.thingsboard.server.dao.orderWork;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.workOrder.EventOverview;
import org.thingsboard.server.dao.util.imodel.query.workOrder.RepairOverviewPageRequest;

import java.util.List;
import java.util.Map;

/**
 * 维修总览服务接口
 *
 * <AUTHOR>
 */
public interface RepairOverviewService {

    /**
     * 分页查询维修记录
     *
     * @param request 查询请求参数
     * @return 分页结果
     */
    IPage<EventOverview> findRepairOverviewByPage(RepairOverviewPageRequest request);

    /**
     * 获取维修统计数据
     *
     * @param request 查询请求参数
     * @return 统计数据
     */
    Map<String, Object> getRepairStatistics(RepairOverviewPageRequest request);

    /**
     * 获取月度统计数据
     *
     * @param request 查询请求参数
     * @return 月度统计数据
     */
    List<Map<String, Object>> getMonthlyStatistics(RepairOverviewPageRequest request);

    /**
     * 获取汇总统计数据
     *
     * @param request 查询请求参数
     * @return 汇总统计数据
     */
    Map<String, Object> getSummaryStatistics(RepairOverviewPageRequest request);

    /**
     * 导出维修记录
     *
     * @param request 查询条件
     * @return 维修记录列表
     */
    List<EventOverview> exportRepairOverview(RepairOverviewPageRequest request);
}
