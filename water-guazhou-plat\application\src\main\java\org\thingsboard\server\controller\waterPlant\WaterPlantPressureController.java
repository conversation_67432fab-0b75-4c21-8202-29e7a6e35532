package org.thingsboard.server.controller.waterPlant;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.waterPlant.WaterPlantPressure;
import org.thingsboard.server.common.data.utils.DateUtils;
import org.thingsboard.server.dao.util.imodel.query.waterPlant.WaterPlantPressurePageRequest;
import org.thingsboard.server.dao.util.imodel.query.waterPlant.WaterPlantPressureSaveRequest;
import org.thingsboard.server.dao.util.imodel.response.ExcelFileInfo;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.dao.util.imodel.response.waterPlant.WaterPlantPressureExportVO;
import org.thingsboard.server.dao.waterPlant.WaterPlantPressureService;
import org.thingsboard.server.utils.imodel.annotations.IStarController2;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 水厂压力信息控制器
 */
@Api(tags = "水厂压力信息管理")
@RestController
@RequestMapping("/api/waterPlant/pressure")
@IStarController2
public class WaterPlantPressureController extends BaseController {

    @Autowired
    private WaterPlantPressureService waterPlantPressureService;

    @ApiOperation(value = "分页查询水厂压力信息")
    @GetMapping
    public IstarResponse findByPage(WaterPlantPressurePageRequest request) throws ThingsboardException {
        request.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        return IstarResponse.ok(waterPlantPressureService.findByPage(request));
    }

    @ApiOperation(value = "新增或更新水厂压力信息")
    @PostMapping
    public IstarResponse save(@RequestBody WaterPlantPressureSaveRequest request) throws ThingsboardException {
        return IstarResponse.ok(waterPlantPressureService.saveOrUpdate(request, getTenantId()));
    }

    @ApiOperation(value = "批量导入水厂压力信息")
    @PostMapping("/import")
    public IstarResponse batchImport(@RequestBody List<WaterPlantPressureSaveRequest> list) throws ThingsboardException {
        waterPlantPressureService.batchImport(list, getTenantId());
        return IstarResponse.ok();
    }

    @ApiOperation(value = "导出水厂压力信息")
    @GetMapping("/export")
    public ExcelFileInfo export(WaterPlantPressurePageRequest request) throws ThingsboardException {
        request.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        List<WaterPlantPressure> list = waterPlantPressureService.exportList(request);

        // 使用ExcelFileInfo导出
        return ExcelFileInfo.of("水厂压力信息", convertToExportVO(list))
                .nextTitle("waterPlantName", "水厂名称")
                .nextTitle("deviceSerial", "设备编号")
                .nextTitle("pressure", "出厂水压(MPa)")
                .nextTitle("dataSource", "数据来源")
                .nextTitle("recordTime", "记录时间")
                .nextTitle("remark", "备注");
    }

    private List<WaterPlantPressureExportVO> convertToExportVO(List<WaterPlantPressure> list) {
        // 转换为导出VO
        List<WaterPlantPressureExportVO> result = new ArrayList<>();
        if (list != null && !list.isEmpty()) {
            for (WaterPlantPressure entity : list) {
                WaterPlantPressureExportVO vo = new WaterPlantPressureExportVO();
                BeanUtils.copyProperties(entity, vo);
                // 确保压力值保留四位小数
                if (vo.getPressure() != null) {
                    vo.setPressure(vo.getPressure().setScale(4, java.math.RoundingMode.HALF_UP));
                }
                result.add(vo);
            }
        }
        return result;
    }

    @ApiOperation(value = "删除水厂压力信息")
    @DeleteMapping("/{id}")
    public IstarResponse delete(@PathVariable String id) {
        return IstarResponse.ok(waterPlantPressureService.delete(id));
    }

    @ApiOperation(value = "获取工艺监控水厂压力数据")
    @GetMapping("/process")
    public IstarResponse getWaterPlantPressureForProcess(
            @RequestParam(required = false) String waterPlantId,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) throws ThingsboardException {

        WaterPlantPressurePageRequest request = new WaterPlantPressurePageRequest();
        request.setWaterPlantId(waterPlantId);

        if (StringUtils.isNotBlank(startTime)) {
            request.setStartTime(DateUtils.str2Date(startTime, DateUtils.DATE_FORMATE_DEFAULT));
        }

        if (StringUtils.isNotBlank(endTime)) {
            request.setEndTime(DateUtils.str2Date(endTime, DateUtils.DATE_FORMATE_DEFAULT));
        }

        request.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));

        // 不分页，返回所有符合条件的数据
        request.setPage(1);
        request.setSize(Integer.MAX_VALUE);

        List<WaterPlantPressure> list = waterPlantPressureService.exportList(request);

        // 转换为工艺监控需要的格式
        List<Map<String, Object>> result = new ArrayList<>();
        for (WaterPlantPressure pressure : list) {
            Map<String, Object> map = new HashMap<>();
            map.put("waterPlantId", pressure.getWaterPlantId());
            map.put("waterPlantName", pressure.getWaterPlantName());
            map.put("deviceSerial", pressure.getDeviceSerial());
            // 确保压力值保留四位小数
            map.put("pressure", pressure.getPressure() != null ? pressure.getPressure().setScale(4, java.math.RoundingMode.HALF_UP) : null);
            map.put("recordTime", pressure.getRecordTime());
            result.add(map);
        }

        return IstarResponse.ok(result);
    }
}
