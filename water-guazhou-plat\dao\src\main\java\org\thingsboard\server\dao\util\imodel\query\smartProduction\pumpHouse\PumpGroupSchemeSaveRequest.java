package org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse.PumpGroupScheme;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;;

@Getter
@Setter
public class PumpGroupSchemeSaveRequest extends SaveRequest<PumpGroupScheme> {
    
    // 所属泵房ID
    @NotNullOrEmpty
    private String pumpRoomId;

    // 方案名称
    @NotNullOrEmpty
    private String schemeName;

    // 方案编码
    @NotNullOrEmpty
    private String schemeCode;

    // 方案描述
    private String schemeDescription;

    // 方案备注
    private String schemeRemark;

    // 泵组配置JSON
    @NotNullOrEmpty
    private String pumpGroupConfig;

    // 是否启用 (0-禁用, 1-启用)
    private Integer status;

    /**
     * 更新 PumpGroupScheme 对象
     *
     * @param id 要更新的对象ID
     * @return 返回更新后的 PumpGroupScheme 对象
     */
    public PumpGroupScheme update(String id) {
        // 实现具体的更新逻辑
        PumpGroupScheme pumpGroupScheme = new PumpGroupScheme();
        pumpGroupScheme.setId(id);
        pumpGroupScheme.setPumpRoomId(this.pumpRoomId);
        pumpGroupScheme.setSchemeName(this.schemeName);
        pumpGroupScheme.setSchemeCode(this.schemeCode);
        // 添加其他字段的设置
        return pumpGroupScheme;
    }

    @Override
    public PumpGroupScheme build() {
        // 实现具体的构建逻辑
        PumpGroupScheme pumpGroupScheme = new PumpGroupScheme();
        pumpGroupScheme.setPumpRoomId(this.pumpRoomId);
        pumpGroupScheme.setSchemeName(this.schemeName);
        pumpGroupScheme.setSchemeCode(this.schemeCode);
        // 添加其他字段的设置
        return pumpGroupScheme;
    }
}
