package org.thingsboard.server.dao.waterPlant;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.waterPlant.WaterPlantPressure;
import org.thingsboard.server.dao.util.imodel.query.waterPlant.WaterPlantPressurePageRequest;
import org.thingsboard.server.dao.util.imodel.query.waterPlant.WaterPlantPressureSaveRequest;

import java.util.List;

/**
 * 水厂压力信息服务接口
 */
public interface WaterPlantPressureService {
    
    /**
     * 分页查询水厂压力信息
     */
    IPage<WaterPlantPressure> findByPage(WaterPlantPressurePageRequest request);
    
    /**
     * 新增水厂压力信息
     * 如果同一水厂同一天已有记录，则更新记录
     */
    WaterPlantPressure saveOrUpdate(WaterPlantPressureSaveRequest request, TenantId tenantId);
    
    /**
     * 批量导入水厂压力信息
     */
    void batchImport(List<WaterPlantPressureSaveRequest> list, TenantId tenantId);
    
    /**
     * 导出水厂压力信息
     */
    List<WaterPlantPressure> exportList(WaterPlantPressurePageRequest request);
    
    /**
     * 删除水厂压力信息
     */
    boolean delete(String id);
}
