package org.thingsboard.server.dao.model.sql.smartManagement.plan;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

/**
 * 巡检任务表单记录实体
 */
@Getter
@Setter
@ResponseEntity
@TableName("sm_circuit_task_form_record")
public class SMCircuitTaskFormRecord {

    /**
     * 主键ID
     */
    @TableId(type = IdType.INPUT)
    private String id;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 任务编号
     */
    private String taskCode;

    /**
     * 巡检配置ID
     */
    private String inspectionConfigId;

    /**
     * 表单项ID（对应表单配置中的某一项）
     */
    private String formItemId;

    /**
     * 巡检地点
     */
    private String location;

    /**
     * 巡检位置
     */
    private String position;

    /**
     * 检验内容
     */
    private String content;

    /**
     * 检验结果
     * NORMAL: 正常
     * ABNORMAL: 异常
     * NOT_CHECKED: 未检查
     */
    private String result;

    /**
     * 检验结果描述
     */
    private String resultDescription;

    /**
     * 附件文件路径（多个用逗号分隔）
     */
    private String attachments;

    /**
     * 检验时间
     */
    private Date checkTime;

    /**
     * 检验人员ID
     */
    private String checkUserId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 租户ID
     */
    private String tenantId;
}
