package org.thingsboard.server.dao.orderWork;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.workOrder.EventOverview;
import org.thingsboard.server.dao.util.imodel.query.workOrder.EventOverviewPageRequest;

import java.util.List;
import java.util.Map;

/**
 * 事件总览服务接口
 * 
 * <AUTHOR>
 */
public interface EventOverviewService {

    /**
     * 分页查询事件总览
     * 
     * @param request 查询请求参数
     * @return 分页结果
     */
    IPage<EventOverview> findEventOverviewByPage(EventOverviewPageRequest request);

    /**
     * 根据ID查询事件详情
     *
     * @param id 事件ID
     * @param tenantId 租户ID
     * @return 事件详情
     */
    EventOverview findEventOverviewById(String id, String tenantId);

    /**
     * 创建事件
     *
     * @param eventData 事件数据
     * @param tenantId 租户ID
     * @return 创建的事件
     */
    EventOverview createEventOverview(Map<String, Object> eventData, String tenantId);

    /**
     * 更新事件
     *
     * @param id 事件ID
     * @param eventData 事件数据
     * @param tenantId 租户ID
     * @return 更新的事件
     */
    EventOverview updateEventOverview(String id, Map<String, Object> eventData, String tenantId);

    /**
     * 删除事件
     *
     * @param id 事件ID
     * @param tenantId 租户ID
     * @return 是否删除成功
     */
    boolean deleteEventOverview(String id, String tenantId);

    /**
     * 更新事件坐标信息
     * 
     * @param id 事件ID
     * @param coordinate 坐标（经度,纬度）
     * @param coordinateName 坐标名称
     * @param tenantId 租户ID
     * @return 是否更新成功
     */
    boolean updateEventCoordinate(String id, String coordinate, String coordinateName, String tenantId);

    /**
     * 批量更新事件坐标信息
     * 
     * @param updates 更新列表，每个Map包含id、coordinate、coordinateName
     * @param tenantId 租户ID
     * @return 更新成功的数量
     */
    int batchUpdateEventCoordinates(List<Map<String, String>> updates, String tenantId);

    /**
     * 获取事件统计信息
     * 
     * @param tenantId 租户ID
     * @param projectId 项目ID（可选）
     * @return 统计信息
     */
    Map<String, Object> getEventStatistics(String tenantId, String projectId);

    /**
     * 获取事件热点地区统计
     * 
     * @param tenantId 租户ID
     * @param limit 返回数量限制
     * @return 热点地区列表
     */
    List<Map<String, Object>> getEventHotspotStatistics(String tenantId, int limit);

    /**
     * 获取事件趋势统计（按天）
     * 
     * @param tenantId 租户ID
     * @param days 统计天数
     * @return 趋势数据
     */
    List<Map<String, Object>> getEventTrendStatistics(String tenantId, int days);

    /**
     * 获取事件状态分布统计
     * 
     * @param tenantId 租户ID
     * @param projectId 项目ID（可选）
     * @return 状态分布数据
     */
    List<Map<String, Object>> getEventStatusDistribution(String tenantId, String projectId);

    /**
     * 获取处理效率统计
     * 
     * @param tenantId 租户ID
     * @param days 统计天数
     * @return 效率统计数据
     */
    Map<String, Object> getProcessEfficiencyStatistics(String tenantId, int days);

    /**
     * 根据地址获取坐标（地理编码）
     * 
     * @param address 地址
     * @return 坐标字符串（经度,纬度）
     */
    String geocodeAddress(String address);

    /**
     * 根据坐标获取地址（逆地理编码）
     * 
     * @param coordinate 坐标（经度,纬度）
     * @return 地址
     */
    String reverseGeocodeCoordinate(String coordinate);

    /**
     * 验证坐标格式是否正确
     * 
     * @param coordinate 坐标字符串
     * @return 是否有效
     */
    boolean isValidCoordinate(String coordinate);

    /**
     * 计算两个坐标之间的距离（米）
     * 
     * @param coordinate1 坐标1
     * @param coordinate2 坐标2
     * @return 距离（米）
     */
    double calculateDistance(String coordinate1, String coordinate2);

    /**
     * 获取指定范围内的事件
     * 
     * @param centerCoordinate 中心坐标
     * @param radiusMeters 半径（米）
     * @param tenantId 租户ID
     * @return 事件列表
     */
    List<EventOverview> findEventsInRadius(String centerCoordinate, double radiusMeters, String tenantId);

    /**
     * 导出事件数据
     * 
     * @param request 查询条件
     * @return 事件列表
     */
    List<EventOverview> exportEventOverview(EventOverviewPageRequest request);

    /**
     * 获取事件处理时长统计
     * 
     * @param tenantId 租户ID
     * @param days 统计天数
     * @return 处理时长统计
     */
    Map<String, Object> getProcessDurationStatistics(String tenantId, int days);

    /**
     * 获取超时事件列表
     * 
     * @param tenantId 租户ID
     * @param limit 返回数量限制
     * @return 超时事件列表
     */
    List<EventOverview> getOverdueEvents(String tenantId, int limit);

    /**
     * 获取高优先级事件列表
     *
     * @param tenantId 租户ID
     * @param limit 返回数量限制
     * @return 高优先级事件列表
     */
    List<EventOverview> getHighPriorityEvents(String tenantId, int limit);

    /**
     * 审核通过并派单
     *
     * @param tenantId 租户ID
     * @param eventId 事件ID
     */
    void approveEvent(String tenantId, String eventId);

    /**
     * 审核通过并创建工单
     *
     * @param tenantId 租户ID
     * @param eventId 事件ID
     * @param assignUserId 指派的处理人ID
     */
    void approveEventAndCreateWorkOrder(String tenantId, String eventId, String assignUserId);

    /**
     * 驳回事件
     *
     * @param tenantId 租户ID
     * @param eventId 事件ID
     * @param reason 驳回原因
     */
    void rejectEvent(String tenantId, String eventId, String reason);

    /**
     * 直接完成事件
     *
     * @param tenantId 租户ID
     * @param eventId 事件ID
     */
    void completeEvent(String tenantId, String eventId);
}
