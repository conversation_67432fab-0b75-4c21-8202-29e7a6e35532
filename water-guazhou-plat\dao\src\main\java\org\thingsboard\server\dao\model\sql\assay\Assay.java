package org.thingsboard.server.dao.model.sql.assay;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


import java.util.Date;

/**
 * 水质化验记录
 */
@Data
@TableName("tb_water_assay")
@ApiModel(value = "水质化验记录", description = "水质化验记录实体类")
public class Assay {

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "创建人ID")
    private String creator;

    @ApiModelProperty(value = "报告名称")
    private String reportName;

    @ApiModelProperty(value = "采样地点")
    private String samplingLocation;

    @ApiModelProperty(value = "检测单位")
    private String testingUnit;

    @ApiModelProperty(value = "检测结果")
    private String testResults;

    @ApiModelProperty(value = "检测日期")
    private Date testDate;

    @ApiModelProperty(value = "报告文件")
    private String reportFile;


    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")

    private Date updateTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "类型")
    private String type;
}
