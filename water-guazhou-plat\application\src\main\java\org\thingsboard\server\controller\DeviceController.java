/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.util.json.Jackson;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.TextNode;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.ListenableFuture;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.repository.query.Param;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.rule.engine.api.msg.DeviceAttributesEventNotificationMsg;
import org.thingsboard.server.common.data.*;
import org.thingsboard.server.common.data.VO.TenantDeviceMsg;
import org.thingsboard.server.common.data.audit.ActionType;
import org.thingsboard.server.common.data.constantsAttribute.*;
import org.thingsboard.server.common.data.device.DeviceFullData;
import org.thingsboard.server.common.data.device.DeviceSearchQuery;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.CustomerId;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.EntityId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.kv.AttributeKvEntry;
import org.thingsboard.server.common.data.kv.BaseAttributeKvEntry;
import org.thingsboard.server.common.data.kv.StringDataEntry;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.common.data.page.TextPageData;
import org.thingsboard.server.common.data.page.TextPageLink;
import org.thingsboard.server.common.data.security.Authority;
import org.thingsboard.server.common.data.security.DeviceCredentials;
import org.thingsboard.server.common.data.utils.StringUtils;
import org.thingsboard.server.common.msg.cluster.SendToClusterMsg;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.dataSource.JobService;
import org.thingsboard.server.dao.device.DeviceTemplateService;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.model.request.PartitionMountRequest;
import org.thingsboard.server.dao.model.sql.DeviceTemplate;
import org.thingsboard.server.dao.shuiwu.WaterMeterIemiService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.aspect.annotation.SysLog;
import org.thingsboard.server.service.constants.ConstantsService;
import org.thingsboard.server.service.rpc.DeviceRpcService;
import org.thingsboard.server.service.security.AccessValidator;
import org.thingsboard.server.service.security.model.SecurityUser;
import org.thingsboard.server.service.telemetry.TelemetrySubscriptionService;
import org.thingsboard.server.utils.AepUtil;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 物联网设备
 */
@RestController
@RequestMapping("/api")
@Slf4j
public class DeviceController extends BaseController {

    public static final String DEVICE_ID = "deviceId";


    @Autowired
    private AccessValidator accessValidator;

    @Autowired
    private TelemetrySubscriptionService tsSubService;

    @Autowired
    private DeviceRpcService deviceRpcService;

    @Autowired
    private ConstantsService constantsService;

    @Autowired
    private DeviceTemplateService deviceTemplateService;

    @Autowired
    private JobService jobService;

    @Autowired
    private AepUtil aepUtil;

    @Autowired
    private WaterMeterIemiService waterMeterIemiService;

    @Value("${hostname.image}")
    private String imageUrl;

    @Value("${install.image_dir}")
    private String imageDir;

    @Value("${water.templateid}")
    private String templateid;
    @Value("${water.tenantid}")
    private String tenantid;
    @Value("${water.projectid}")
    private String projectid;
    @Value("${water.gatewayid}")
    private String gatewayid;
    @Value("${water.aep.model}")
    private String model;
    @Value("${water.aep.enable}")
    private Boolean enable;


    /**
     * 根据设备id获取设备详情
     *
     * @param strDeviceId
     * @return
     * @throws ThingsboardException
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/device/{deviceId}", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_DEVICE_INFO)
    @ResponseBody
    public Device getDeviceById(@PathVariable(DEVICE_ID) String strDeviceId) throws ThingsboardException {
        checkParameter(DEVICE_ID, strDeviceId);
        try {
            DeviceId deviceId = new DeviceId(toUUID(strDeviceId));
            return checkDeviceId(deviceId);
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    /**
     * 根据设备id获取设备详情（包含项目信息）
     *
     * @param deviceId
     * @return
     * @throws ThingsboardException
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/device/deviceInfo/{deviceId}", method = RequestMethod.GET)
    @ResponseBody
    public Device deviceInfo(@PathVariable String deviceId) throws ThingsboardException {
        DeviceId id = new DeviceId(UUIDConverter.fromString(deviceId));
        return deviceService.getDeviceInfo(id);
    }


    /**
     * 添加设备
     *
     * @param device
     * @param needUpdateGateway
     * @return
     * @throws ThingsboardException
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/device", method = RequestMethod.POST)
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_DEVICE_ADD)
    @ResponseBody
    public Device saveDevice(@RequestBody Device device, @RequestParam(value = "needUpdateGateway", required = false) boolean needUpdateGateway) throws ThingsboardException {
        try {
            device.setTenantId(getTenantId());
            SecurityUser user = getCurrentUser();
            if (user.getAuthority() == Authority.CUSTOMER_USER) {
                if (device.getId() == null || device.getId().isNullUid() ||
                        device.getCustomerId() == null || device.getCustomerId().isNullUid()) {
                    throw new ThingsboardException("You don't have permission to perform this operation!",
                            ThingsboardErrorCode.PERMISSION_DENIED);
                } else {
                    checkCustomerId(device.getCustomerId());
                }
            }
            //更新设备属性
            if (StringUtils.checkNotNull(device.getTemplateId())) {
                if (!StringUtils.checkNotNull(device.getInfo())) {
                    device.setAttribute(constantsService.getConstantsByTypeAndKey(DataConstants.CONSTANTS_DEVICE_TEMPLATE, ModelConstants.ATTRIBUTE).get().get(0).getValue());
                }
                if (!StringUtils.checkNotNull(device.getProp())) {
                    DeviceTemplate template = deviceTemplateService.findById(device.getTemplateId());
                    if (!StringUtils.checkNotNull(template.getProtocol())) {
                        throw new ThingsboardException("设备协议变量为空，请添加协议变量!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
                    }
                    device.setProp(template.getProtocol());
                }
            }
            Device savedDevice = checkNotNull(deviceService.saveDevice(device));
            //更新掉线报警时间
            if (savedDevice.getAdditionalInfo() != null) {
                JsonNode jsonNode = savedDevice.getAdditionalInfo();
                try {
                    String offline = jsonNode.get("dropJudgement").asText();
                    if (offline != null) {
                        savedDevice.setOfflineInterval(offline);
                    } else {
                        savedDevice.setOfflineInterval(DataConstants.REQUEST_DATA_STEPSIZE_DEFAULT);
                    }
                } catch (Exception e) {
                    savedDevice.setOfflineInterval(DataConstants.REQUEST_DATA_STEPSIZE_DEFAULT);
                }
            }
            jobService.modifyOffline(savedDevice);
            List<Device> devices = deviceService.findDeviceByGateWayId(savedDevice.getId());
            if (devices.size() > 0) {
                devices.forEach(device1 -> {
                    device1.setOfflineInterval(savedDevice.getOfflineInterval());
                    jobService.modifyOffline(device);
                });
            }

            if (device.getProp() != null) {
                dataSourceService.saveDeviceDataSource(getTenantId(), UUIDConverter.fromTimeUUID(savedDevice.getUuidId()), device.getProp());
            }
            // 挂载设备到网关
            JSONObject additionalInfo = JSON.parseObject(savedDevice.getAdditionalInfo().asText());
            if (StringUtils.checkNotNull(device.getInfo()) && StringUtils.checkNotNull(device.getProp())) {
                attributesService.save(savedDevice.getId(), DataConstants.SHARED_SCOPE, new BaseAttributeKvEntry(new StringDataEntry(ModelConstants.ATTRIBUTE, device.getInfo()), System.currentTimeMillis()));
                attributesService.save(savedDevice.getId(), DataConstants.SHARED_SCOPE, new BaseAttributeKvEntry(new StringDataEntry(ModelConstants.PROR, device.getProp()), System.currentTimeMillis()));
                savedDevice.setProp(device.getProp());
                savedDevice.setAttribute(device.getInfo());
            }
            //当修改的设备挂载到某个网关下面时，进行配置文件的更新
            if (device.getGateWayId() != null) {
                if (needUpdateGateway) {
                    updateGateWay(savedDevice, savedDevice.getGateWayId(), user);
                } else if ((additionalInfo == null || (additionalInfo.getBoolean("isVirtualDevice") != null && !additionalInfo.getBoolean("isVirtualDevice")))) {
                    updateGatewayName(savedDevice, savedDevice.getGateWayId(), user);
                }
            }
            actorService
                    .onDeviceNameOrTypeUpdate(
                            savedDevice.getTenantId(),
                            savedDevice.getId(),
                            savedDevice.getName(),
                            savedDevice.getType());
            logEntityAction(savedDevice.getId(), savedDevice,
                    savedDevice.getCustomerId(),
                    device.getId() == null ? ActionType.ADDED : ActionType.UPDATED, null);

            if (device.getId() == null) {
                deviceStateService.onDeviceAdded(savedDevice);
            } else {
                deviceStateService.onDeviceUpdated(savedDevice);
            }
            return savedDevice;
        } catch (
                Exception e) {
            logEntityAction(emptyId(EntityType.DEVICE), device,
                    null, device.getId() == null ? ActionType.ADDED : ActionType.UPDATED, e);
            throw handleException(e);
        }
    }

    /**
     * 更新下发配置中的设备名称
     *
     * @param savedDevice
     * @param user
     */
    private void updateGatewayName(Device savedDevice, DeviceId gateWayId, SecurityUser user) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            //获取网关
            //List<Device> gateWays = deviceService.findDevicesByTenantIdAndType(getTenantId(), ModelConstants.GATEWAY, new TextPageLink(20)).getData();
            Device gateWay = deviceService.findDeviceById(gateWayId);
            if (gateWay != null) {
                //获取网关属性
                Optional<AttributeKvEntry> attributeKvEntitry = attributesService.find(getTenantId(), gateWay.getId(), DataConstants.SHARED_SCOPE, ModelConstants.CONFIGURATION).get();
                List<GateWayAttribute> gateAttributes = new ArrayList<>();
                // 更新网关设备
                if (attributeKvEntitry.isPresent()) {
                    List<GateWayAttribute> gateWayAttributes = objectMapper.readValue(attributeKvEntitry.get().getValueAsString(), new TypeReference<List<GateWayAttribute>>() {
                    });
                    gateWayAttributes.forEach(gateWayAttribute -> {
                        for (Server server : gateWayAttribute.getConfiguration().getServers()) {
                            for (GateWayDevice device : server.getDevices()) {
                                if (device.getDeviceId().equals(UUIDConverter.fromTimeUUID(savedDevice.getId().getId()))) {
                                    // 仅更换name
                                    device.setDeviceName(savedDevice.getName());
                                }
                            }
                        }
                        gateAttributes.add(gateWayAttribute);
                    });

                }
                List<AttributeKvEntry> attributes = new ArrayList<>();
                attributes.add(new BaseAttributeKvEntry(new StringDataEntry(ModelConstants.CONFIGURATION, Jackson.toJsonString(gateAttributes)), System.currentTimeMillis()));
                //更新完扩展数据，更新网关attribute
                updateAttribute(user, gateWay.getId(), attributes);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //更新扩展信息
    public void updateGateWay(Device savedDevice, DeviceId gateWayId, SecurityUser user) throws ThingsboardException {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            //获取网关(修改为从创建设备获取)
            //List<Device> gateWays = deviceService.findDevicesByTenantIdAndType(getTenantId(), ModelConstants.GATEWAY, new TextPageLink(20)).getData();
            Device gateWay = deviceService.findDeviceById(gateWayId);
            if (gateWay != null) {
                //获取网关属性
                Optional<AttributeKvEntry> attributeKvEntitry = attributesService.find(getTenantId(), gateWay.getId(), DataConstants.SHARED_SCOPE, ModelConstants.CONFIGURATION).get();
                //如果还没有扩展关联，则重新生成扩展数据,如果已经有数据，则更新扩展数据
                List<GateWayAttribute> gateAttributes = new ArrayList<>();
                final boolean[] a = {false};
                // 当前新增设备
                GateWayAttribute getWayMsg = createGetWayMsg(savedDevice, gateWay.getType());
                // 拿到刚新增的设备
                List<GateWayDevice> devices = getWayMsg.getConfiguration().getServers().get(0).getDevices();
                if (attributeKvEntitry.isPresent()) {// 存在扩展属性，判断是否为更新操作
                    List<GateWayAttribute> gateWayAttributes = objectMapper.readValue(attributeKvEntitry.get().getValueAsString(), new TypeReference<List<GateWayAttribute>>() {
                    });
                    gateWayAttributes.forEach(gateWayAttribute -> {
                        // 遍历设备列表
                        for (Server server : gateWayAttribute.getConfiguration().getServers()) {
                            if (server.getTransport().getType().equalsIgnoreCase(DataConstants.PROTOCOL_TYPE_MODBUS_RTU)) {
                                // 先遍历当前Server中的com口是否一致
                                int deviceIndex = -1;
                                for (GateWayDevice d : server.getDevices()) {
                                    if (d.getDeviceId().equals(UUIDConverter.fromTimeUUID(savedDevice.getId().getId()))) {
                                        // 判断要更新的设备COM口是否一致
                                        if (server.getTransport().getPortName() != null && server.getTransport().getPortName().equals(getWayMsg.getConfiguration().getServers().get(0).getTransport().getPortName())) {
                                            // 一致，更新设备信息
                                            d.setAttributesPollPeriod(devices.get(0).getAttributesPollPeriod());
                                            d.setDeviceName(devices.get(0).getDeviceName());
                                            d.setTimeseries(devices.get(0).getTimeseries());
                                            d.setAttributes(devices.get(0).getAttributes());
                                            d.setTimeseriesPollPeriod(devices.get(0).getTimeseriesPollPeriod());
                                            d.setUnitId(devices.get(0).getUnitId());
                                            d.setSingleRead(devices.get(0).getSingleRead());
                                            d.setDeviceReadRegisterNumberByOnce(devices.get(0).getDeviceReadRegisterNumberByOnce());
                                            d.setWireSystem(devices.get(0).getWireSystem());
                                            a[0] = true;
                                        } else {
                                            // COM口不一致，删除该Server下的该设备信息
                                            if (server.getDevices().size() > 1) {
                                                deviceIndex = server.getDevices().indexOf(d);
                                            } else {
                                                server.setDevices(new ArrayList<>());
                                            }
                                        }
                                    }
                                }
                                // 删除设备信息
                                if (deviceIndex != -1) {
                                    server.getDevices().remove(deviceIndex);
                                }
                                // 当前遍历到和新增设备一致的COM口时
                                if (!a[0] && server.getTransport().getPortName() != null && server.getTransport().getPortName().equals(getWayMsg.getConfiguration().getServers().get(0).getTransport().getPortName())) {
                                    // 将设备放进该server中
                                    server.getDevices().add(getWayMsg.getConfiguration().getServers().get(0).getDevices().get(0));
                                    a[0] = true;
                                }
                            } else {
                                int deviceIndex = -1;
                                for (GateWayDevice d : server.getDevices()) {
                                    if (d.getDeviceId().equals(UUIDConverter.fromTimeUUID(savedDevice.getId().getId()))) {
                                        // 判断要更新的设备COM口是否一致
                                        if (server.getTransport().getHost().equals(getWayMsg.getConfiguration().getServers().get(0).getTransport().getHost()) &&
                                                server.getTransport().getPort().equalsIgnoreCase(getWayMsg.getConfiguration().getServers().get(0).getTransport().getPort())) {
                                            // 一致，更新设备信息
                                            d.setAttributes(devices.get(0).getAttributes());
                                            d.setAttributesPollPeriod(devices.get(0).getAttributesPollPeriod());
                                            d.setUnitId(devices.get(0).getUnitId());
                                            d.setDeviceName(devices.get(0).getDeviceName());
                                            d.setTimeseries(devices.get(0).getTimeseries());
                                            d.setTimeseriesPollPeriod(devices.get(0).getTimeseriesPollPeriod());
                                            d.setSingleRead(devices.get(0).getSingleRead());
                                            d.setDeviceReadRegisterNumberByOnce(devices.get(0).getDeviceReadRegisterNumberByOnce());
                                            d.setWireSystem(devices.get(0).getWireSystem());
                                            a[0] = true;
                                        } else {
                                            // 删除该Server下的该设备信息
                                            if (server.getDevices().size() > 1) {
                                                deviceIndex = server.getDevices().indexOf(d);
                                            } else {
                                                server.setDevices(new ArrayList<>());
                                            }
                                        }
                                    }
                                }
                                // 删除设备信息
                                if (deviceIndex != -1) {
                                    server.getDevices().remove(deviceIndex);
                                }
                                // 当前遍历到和新增设备一致的COM口时
                                if (!a[0] && server.getTransport().getHost().equals(getWayMsg.getConfiguration().getServers().get(0).getTransport().getHost()) &&
                                        server.getTransport().getPort().equalsIgnoreCase(getWayMsg.getConfiguration().getServers().get(0).getTransport().getPort())) {
                                    // 将设备放进该server中
                                    server.getDevices().add(getWayMsg.getConfiguration().getServers().get(0).getDevices().get(0));
                                    a[0] = true;
                                }
                            }
                        }
                        gateAttributes.add(gateWayAttribute);
                    });
                    // 新增
                    if (!a[0]) {
                        // 合并设备集合
                        List<Server> servers = gateAttributes.get(0).getConfiguration().getServers();
                        servers.addAll(getWayMsg.getConfiguration().getServers());

                        // 保存
                        gateAttributes.get(0).getConfiguration().setServers(servers);
                    }
                }
                //
                else {
                    gateAttributes.add(createGetWayMsg(savedDevice, gateWay.getType()));
                }
                // 去除没有设备的server
                gateAttributes.get(0).getConfiguration().setServers(gateAttributes.get(0)
                        .getConfiguration()
                        .getServers()
                        .stream()
                        .filter(s -> s.getDevices().size() > 0).collect(Collectors.toList()));
                List<AttributeKvEntry> attributes = new ArrayList<>();
                //保存gateWayId
                savedDevice.setGateWayId(gateWayId);
                deviceService.saveDevice(savedDevice);
                attributes.add(new BaseAttributeKvEntry(new StringDataEntry(ModelConstants.CONFIGURATION, Jackson.toJsonString(gateAttributes)), System.currentTimeMillis()));
                //更新完扩展数据，更新网关attribute
                updateAttribute(user, gateWay.getId(), attributes);
            }
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    //更新attribute
    private void updateAttribute(SecurityUser user, EntityId uuid, final List<AttributeKvEntry> attributes) throws ThingsboardException {
        accessValidator.validateEntityAndCallback(user, uuid, (result, TenantId, entityId) -> {
            tsSubService.saveAndNotify(entityId, DataConstants.SHARED_SCOPE, attributes, new FutureCallback<Void>() {
                @Override
                public void onSuccess(@Nullable Void tmp) {
                    if (entityId.getEntityType() == EntityType.DEVICE) {
                        DeviceId deviceId = new DeviceId(entityId.getId());
                        DeviceAttributesEventNotificationMsg notificationMsg = DeviceAttributesEventNotificationMsg.onUpdate(
                                user.getTenantId(), deviceId, DataConstants.SHARED_SCOPE, attributes);
                        actorService.onMsg(new SendToClusterMsg(deviceId, notificationMsg));
                    }
                }

                @Override
                public void onFailure(Throwable t) {
                    AccessValidator.handleError(t, result, HttpStatus.INTERNAL_SERVER_ERROR);
                }
            });
        });
    }

    /**
     * 创建设备时新建网关下发扩展
     *
     * @param device
     * @return
     */
    public GateWayAttribute createGetWayMsg(Device device, String gatewayType) throws ThingsboardException {
        //设备属性
        try {
            if (device.getInfo() == null || device.getProp() == null) {
                device.setProp(attributesService.findNotFuture(device.getId(), DataConstants.SHARED_SCOPE, ModelConstants.PROR).getValueAsString());
                device.setAttribute(attributesService.findNotFuture(device.getId(), DataConstants.SHARED_SCOPE, ModelConstants.ATTRIBUTE).getValueAsString());
            }
            ObjectMapper objectMapper = new ObjectMapper();
            DeviceAttribute deviceAttribute = objectMapper.readValue(device.getInfo(), new TypeReference<DeviceAttribute>() {
            });
            List<PropAttribute> propAttributes = objectMapper.readValue(device.getProp(), new TypeReference<List<PropAttribute>>() {
            });
            Transport transport = null;
            // 判断type
            if (deviceAttribute.getModbusType().equalsIgnoreCase(DataConstants.PROTOCOL_TYPE_MODBUS_TCP)) {
                transport = new Transport(DataConstants.PROTOCOL_TYPE_MODBUS_TCP, deviceAttribute.getTimeout(), deviceAttribute.getHost(), deviceAttribute.getPort());
            } else if (deviceAttribute.getModbusType().equalsIgnoreCase(DataConstants.PROTOCOL_TYPE_MODBUS_RTU)) {
                transport = new Transport(DataConstants.PROTOCOL_TYPE_MODBUS_RTU, deviceAttribute.getTimeout(),
                        deviceAttribute.getPortName(), DataConstants.PROTOCOL_TYPE_MODBUS_RTU, deviceAttribute.getBaudRate(),
                        deviceAttribute.getDataBits(), deviceAttribute.getStopBits(), deviceAttribute.getParity());
            }
            List<Timeseries> timeseries = new ArrayList<>();
            propAttributes.forEach(propAttribute -> {
                if (propAttribute.getIsVirtual() == null || !propAttribute.getIsVirtual().equalsIgnoreCase(DataConstants.TRUE)) {
                    timeseries.add(Timeseries.builder().tag(propAttribute.getPropertyCategory())
                            .pollPeriod(propAttribute.getPollPeriod())
                            .type(propAttribute.getDataType())
                            .functionCode(propAttribute.getFunctionCode())
                            .address(propAttribute.getRegisterAddress())
                            .registerCount(propAttribute.getByteCount())
                            .byteOrder(propAttribute.getByteOrder())
                            .order(propAttribute.getOrder())
                            .bit(propAttribute.getBitPosition())
                            .sampleDeviation(propAttribute.getSampleDeviation())
                            .samplingMax(propAttribute.getSamplingMax())
                            .samplingMin(propAttribute.getSamplingMin())
                            .formulaProperty(propAttribute.getFormulaProperty())
                            .sampleCoef(propAttribute.getSampleCoef())
                            .range(propAttribute.getRange())
                            .statType(propAttribute.getStatType())
                            .propertyType(propAttribute.getPropertyType())
                            .group(propAttribute.getGroup())
                            .serialNumber(propAttribute.getSerialNumber())
                            .pointAddress(propAttribute.getPointAddress())
                            .invalidValue(propAttribute.getInvalidValue())
                            .build());
                }
            });
            //GateWayDevice gateWayDevice = new GateWayDevice(deviceAttribute.getUnitId(), UUIDConverter.fromTimeUUID(device.getId().getId()), device.getName(), deviceAttribute.getTimeout(), deviceAttribute.getTimeout(), new ArrayList<>(), timeseries, deviceAttribute.getSingleRead(), deviceAttribute.getDeviceReadRegisterNumberByOnce());
            Server server = new Server(transport, Collections.singletonList(GateWayDevice.builder()
                    .unitId(deviceAttribute.getUnitId())
                    .deviceId(UUIDConverter.fromTimeUUID(device.getUuidId()))
                    .deviceName(device.getName())
                    .attributesPollPeriod(deviceAttribute.getTimeout())
                    .timeseriesPollPeriod(deviceAttribute.getTimeout())
                    .attributes(new ArrayList<>())
                    .timeseries(timeseries)
                    .singleRead(deviceAttribute.getSingleRead())
                    .deviceReadRegisterNumberByOnce(deviceAttribute.getDeviceReadRegisterNumberByOnce())
                    .build()));
            return new GateWayAttribute(UUIDConverter.fromTimeUUID(device.getGateWayId().getId()), gatewayType, new Configuration(Collections.singletonList(server)));
        } catch (Exception e) {
            throw handleException(e);
//            e.printStackTrace();
        }
    }

    /**
     * 删除设备
     *
     * @param strDeviceId
     * @param needUpdateGateway
     * @throws ThingsboardException
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/device/{deviceId}", method = RequestMethod.DELETE)
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_DEVICE_DELETE)
    @ResponseStatus(value = HttpStatus.OK)
    public void deleteDevice(@PathVariable(DEVICE_ID) String strDeviceId, @RequestParam(value = "needUpdateGateway", required = false) boolean needUpdateGateway) throws ThingsboardException {
        checkParameter(DEVICE_ID, strDeviceId);
        try {
            DeviceId deviceId = new DeviceId(toUUID(strDeviceId));
            Device device = checkDeviceId(deviceId);
            if (needUpdateGateway) {
                if (device.getGateWayId() != null) {
                    deleteGateWayInfo(deviceId, device.getGateWayId());
                } else {
                    List<Device> gateWays = deviceService.findDevicesByTenantIdAndType(getTenantId(), ModelConstants.GATEWAY, new TextPageLink(20)).getData();
                    if (gateWays.size() == 1) {
                        deleteGateWayInfo(deviceId, gateWays.get(0).getId());
                    }
                }
            }
            try {
                deviceService.deleteDevice(getTenantId(), deviceId);
            } catch (Exception e) {
                e.printStackTrace();
            }
            //删除设备对应的报警
            alarmService.deleteAlarmByDevice(deviceId);
            logEntityAction(deviceId, device,
                    device.getCustomerId(),
                    ActionType.DELETED, null, strDeviceId);
            deviceStateService.onDeviceDeleted(device);
        } catch (Exception e) {
            logEntityAction(emptyId(EntityType.DEVICE),
                    null,
                    null,
                    ActionType.DELETED, e, strDeviceId);
            throw handleException(e);
        }
    }


    /**
     * 设备上线
     *
     * @param deviceId
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/device/online", method = RequestMethod.DELETE)
    @SysLog(detail = DataConstants.OPERATING_TYPE_DEVICE_ONLINE)
    @ResponseStatus(value = HttpStatus.OK)
    public void deviceOnline(@Param("deviceId") String deviceId) {
        deviceService.updateDeviceOnline(new DeviceId(UUIDConverter.fromString(deviceId)));
    }

    /**
     * 删除设备时删除该设备的扩展信息
     *
     * @throws ThingsboardException
     */
    public void deleteGateWayInfo(DeviceId deviceId, DeviceId gateWayId) throws ThingsboardException {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            //获取网关
            // List<Device> gateWays = deviceService.findDevicesByTenantIdAndType(getTenantId(), ModelConstants.GATEWAY, new TextPageLink(20)).getData();
            Device gateWay = deviceService.findDeviceById(gateWayId);
            if (gateWay != null) {
                //获取网关属性
                List<AttributeKvEntry> attributeKvEntities = attributesService.findAll(getTenantId(), gateWay.getId(), DataConstants.SHARED_SCOPE).get();
                if (attributeKvEntities != null && attributeKvEntities.size() > 0) {
                    List<GateWayAttribute> gateWayAttributes = objectMapper.readValue(attributeKvEntities.get(0).getValueAsString(), new TypeReference<List<GateWayAttribute>>() {
                    });
                    List<GateWayAttribute> list = new ArrayList<>();

                    // 遍历每个server
                    gateWayAttributes.get(0).getConfiguration().getServers().forEach(server -> {
                        int deleteServiceIndex = -1;
                        boolean findFlag = false;
                        List<GateWayDevice> devices = server.getDevices();
                        for (GateWayDevice device : devices) {
                            if (device.getDeviceId().equals(UUIDConverter.fromTimeUUID(deviceId.getId()))) {
                                deleteServiceIndex = devices.indexOf(device);
                                findFlag = true;
                                break;
                            }
                        }
                        // 移除设备
                        if (findFlag) {
                            // 判断是否有其他设备
                            if (server.getDevices().size() > 1) {
                                server.getDevices().remove(deleteServiceIndex);
                            } else {
                                server.setDevices(new ArrayList<>());
                            }
                        }
                    });
                    // 去除没有设备的server
                    gateWayAttributes.get(0).getConfiguration().setServers(gateWayAttributes.get(0).getConfiguration().getServers().stream().filter(s -> s.getDevices().size() > 0).collect(Collectors.toList()));
                    List<AttributeKvEntry> attributes = new ArrayList<>();
                    attributes.add(new BaseAttributeKvEntry(new StringDataEntry(ModelConstants.CONFIGURATION, Jackson.toJsonString(gateWayAttributes)), System.currentTimeMillis()));
                    //更新完扩展数据，更新网关attribute
                    updateAttribute(getCurrentUser(), gateWay.getId(), attributes);
                }
            }
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    /**
     * 获取设备token
     *
     * @param strDeviceId
     * @return
     * @throws ThingsboardException
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/device/{deviceId}/credentials", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_DEVICE_CREDENTIALS_INFO)
    @ResponseBody
    public DeviceCredentials getDeviceCredentialsByDeviceId(@PathVariable(DEVICE_ID) String strDeviceId) throws ThingsboardException {
        checkParameter(DEVICE_ID, strDeviceId);
        try {
            DeviceId deviceId = new DeviceId(toUUID(strDeviceId));
            Device device = checkDeviceId(deviceId);
            DeviceCredentials deviceCredentials = checkNotNull(deviceCredentialsService.findDeviceCredentialsByDeviceId(getTenantId(), deviceId));
            logEntityAction(deviceId, device,
                    device.getCustomerId(),
                    ActionType.CREDENTIALS_READ, null, strDeviceId);
            return deviceCredentials;
        } catch (Exception e) {
            logEntityAction(emptyId(EntityType.DEVICE), null,
                    null,
                    ActionType.CREDENTIALS_READ, e, strDeviceId);
            throw handleException(e);
        }
    }

    /**
     * 更新设备token
     *
     * @param deviceCredentials
     * @return
     * @throws ThingsboardException
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/device/credentials", method = RequestMethod.POST)
    @SysLog(detail = DataConstants.OPERATING_TYPE_DEVICE_CREDENTIALS_ADD)
    @ResponseBody
    public DeviceCredentials saveDeviceCredentials(@RequestBody DeviceCredentials deviceCredentials) throws ThingsboardException {
        checkNotNull(deviceCredentials);
        try {
            Device device = checkDeviceId(deviceCredentials.getDeviceId());
            DeviceCredentials result = checkNotNull(deviceCredentialsService.updateDeviceCredentials(getTenantId(), deviceCredentials));
            actorService.onCredentialsUpdate(getTenantId(), deviceCredentials.getDeviceId());
            logEntityAction(device.getId(), device,
                    device.getCustomerId(),
                    ActionType.CREDENTIALS_UPDATED, null, deviceCredentials);
            return result;
        } catch (Exception e) {
            logEntityAction(emptyId(EntityType.DEVICE), null,
                    null,
                    ActionType.CREDENTIALS_UPDATED, e, deviceCredentials);
            throw handleException(e);
        }
    }

    /**
     * 获取租户下的设备分页列表
     * @param limit
     * @param type
     * @param textSearch
     * @param idOffset
     * @param textOffset
     * @return
     * @throws ThingsboardException
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/tenant/devices", params = {"limit"}, method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_DEVICE_LIST)
    @ResponseBody
    public TextPageData<Device> getTenantDevices(
            @RequestParam int limit,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String textSearch,
            @RequestParam(required = false) String idOffset,
            @RequestParam(required = false) String textOffset) throws ThingsboardException {
        try {
            TenantId tenantId = getTenantId();
            TextPageLink pageLink = createPageLink(limit, textSearch, idOffset, textOffset);
            if (type != null && type.trim().length() > 0) {
                return checkNotNull(deviceService.findDevicesByTenantIdAndType(tenantId, type, pageLink));
            } else {
                return checkNotNull(deviceService.findDevicesByTenantId(tenantId, pageLink));
            }
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    /**
     * 获取租户下的所有设备
     * @param key
     * @return
     * @throws ThingsboardException
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN','TENANT_SUPPORT','TENANT_PROMOTE', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/tenant/devicesAll", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_DEVICE_LIST)
    @ResponseBody
    public List<Device> getTenantDevicesAll(@RequestParam("key") String key) throws ThingsboardException {
        return checkNotNull(deviceService.findAllByTenantId(getTenantId(), key));
    }

    /**
     * 根据项目id获取设备列表
     * @param key
     * @param projectId
     * @return
     * @throws ThingsboardException
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/project/devicesAll/{projectId}", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_DEVICE_LIST)
    @ResponseBody
    public List<Device> getProjectDevicesAll(@RequestParam("key") String key, @PathVariable String projectId) throws ThingsboardException {
        return checkNotNull(deviceService.findAllByProjectId(projectId, key));
    }

    /**
     * 获取租户下的所有设备
     * @return
     * @throws ThingsboardException
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/tenant/devicesByTenantId", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_DEVICE_LIST)
    @ResponseBody
    public List<Device> getTenantDevicesAll() throws ThingsboardException {
        try {
            TenantId tenantId = getTenantId();
            return checkNotNull(
                    deviceService.findAllByTenantId(tenantId));
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    /**
     * 按名字获取租户下的所有设备
     * @param deviceName
     * @return
     * @throws ThingsboardException
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/tenant/devices", params = {"deviceName"}, method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_DEVICE_LIST)
    @ResponseBody
    public Device getTenantDevice(
            @RequestParam String deviceName) throws ThingsboardException {
        try {
            TenantId tenantId = getTenantId();
            return deviceService.findDeviceByTenantIdAndName(tenantId, deviceName);
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    /**
     * 获取普通用户下的设备列表
     * @param strCustomerId
     * @param limit
     * @param type
     * @param textSearch
     * @param idOffset
     * @param textOffset
     * @return
     * @throws ThingsboardException
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/customer/{customerId}/devices", params = {"limit"}, method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_DEVICE_LIST)
    @ResponseBody
    public TextPageData<Device> getCustomerDevices(
            @PathVariable("customerId") String strCustomerId,
            @RequestParam int limit,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String textSearch,
            @RequestParam(required = false) String idOffset,
            @RequestParam(required = false) String textOffset) throws ThingsboardException {
        checkParameter("customerId", strCustomerId);
        try {
            TenantId tenantId = getTenantId();
            CustomerId customerId = new CustomerId(toUUID(strCustomerId));
            checkCustomerId(customerId);
            TextPageLink pageLink = createPageLink(limit, textSearch, idOffset, textOffset);
            if (type != null && type.trim().length() > 0) {
                return checkNotNull(deviceService.findDevicesByTenantIdAndCustomerIdAndType(tenantId, customerId, type, pageLink));
            } else {
                return checkNotNull(deviceService.findDevicesByTenantIdAndCustomerId(tenantId, customerId, pageLink));
            }
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    /**
     * 根据设备id获取设备列表
     * @param strDeviceIds
     * @return
     * @throws ThingsboardException
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/devices", params = {"deviceIds"}, method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_DEVICE_LIST)
    @ResponseBody
    public List<Device> getDevicesByIds(
            @RequestParam("deviceIds") String[] strDeviceIds) throws ThingsboardException {
        checkArrayParameter("deviceIds", strDeviceIds);
        try {
            SecurityUser user = getCurrentUser();
            TenantId tenantId = user.getTenantId();
            CustomerId customerId = user.getCustomerId();
            List<DeviceId> deviceIds = new ArrayList<>();
            for (String strDeviceId : strDeviceIds) {
                deviceIds.add(new DeviceId(toUUID(strDeviceId)));
            }
            ListenableFuture<List<Device>> devices;
            if (customerId == null || customerId.isNullUid()) {
                devices = deviceService.findDevicesByTenantIdAndIdsAsync(tenantId, deviceIds);
            } else {
                devices = deviceService.findDevicesByTenantIdCustomerIdAndIdsAsync(tenantId, customerId, deviceIds);
            }
            return checkNotNull(devices.get().stream()
                    .filter(d -> d.getIsDelete().equals(DataConstants.IS_DELETE_NO))
                    .collect(Collectors.toList()));
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    /**
     * 按条件查询设备列表
     * @param query
     * @return
     * @throws ThingsboardException
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/devices", method = RequestMethod.POST)
    @SysLog(detail = DataConstants.OPERATING_TYPE_DEVICE_LIST)
    @ResponseBody
    public List<Device> findByQuery(@RequestBody DeviceSearchQuery query) throws ThingsboardException {
        checkNotNull(query);
        checkNotNull(query.getParameters());
        checkNotNull(query.getDeviceTypes());
        checkEntityId(query.getParameters().getEntityId());
        try {
            List<Device> devices = checkNotNull(deviceService.findDevicesByQuery(getTenantId(), query).get());
            devices = devices.stream().filter(device -> {
                try {
                    checkDeviceId(device.getId());
                    return true;
                } catch (ThingsboardException e) {
                    return false;
                }
            }).collect(Collectors.toList());
            return devices;
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    /**
     * 获取设备类型列表
     * @return
     * @throws ThingsboardException
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/device/types", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_DEVICE_TYPE)
    @ResponseBody
    public List<EntitySubtype> getDeviceTypes() throws ThingsboardException {
        try {
            SecurityUser user = getCurrentUser();
            TenantId tenantId = user.getTenantId();
            ListenableFuture<List<EntitySubtype>> deviceTypes = deviceService.findDeviceTypesByTenantId(tenantId);
            return checkNotNull(deviceTypes.get());
        } catch (Exception e) {
            throw handleException(e);
        }
    }


    /**
     * 根据tenantId获取设备列表
     *
     * @return
     * @throws ThingsboardException
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/tenant/gateway", method = RequestMethod.GET)
//    @SysLog(detail = DataConstants.OPERATING_TYPE_GATEWAY_LIST)
    @ResponseBody
    public List<Device> getTenantGateWay() throws ThingsboardException {
        try {
            TenantId tenantId = getTenantId();
            return checkNotNull(deviceService.findGateWayByTenantId(tenantId));
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    /**
     * 根据projectId获取设备列表
     *
     * @return
     * @throws ThingsboardException
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/project/devices/{projectId}", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_DEVICE_LIST)
    @ResponseBody
    public List<Device> getDeviceByProject(@PathVariable String projectId) throws ThingsboardException {
        try {
            return checkNotNull(deviceService.findByProjectId(projectId, getTenantId()).stream().filter(device -> {
                if (device.getAdditionalInfo() != null) {
                    JSONObject additionalInfo = JSON.parseObject(device.getAdditionalInfo().asText());
                    if ((!device.getType().equals("MQTT") && !device.getType().equals("DTU")
                            && !device.getType().equals("NBMQTT") && !device.getType().equals("MODBUS"))
                            && additionalInfo != null && additionalInfo.containsKey("gateway")) {
                        return false;
                    }
                    if (device.getGateWayId() == null && !device.getType().contains("MQTT")) {
                        return false;
                    }
                }
                return true;
            }).collect(Collectors.toList()));
        } catch (Exception e) {
            throw handleException(e);
        }
    }


    /**
     * 根据tenantId获取设备列表
     *
     * @return
     * @throws ThingsboardException
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/tenant/allDevice/mqttAndDevice", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_GATEWAY_LIST)
    @ResponseBody
    public List<Device> getMqttDeviceAndDevices() throws ThingsboardException {
        try {
            TenantId tenantId = getTenantId();
            return checkNotNull(deviceService.getMqttDeviceAndDevices(tenantId));
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    /**
     * 根据projectId获取设备列表
     *
     * @return
     * @throws ThingsboardException
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/tenant/deviceSearchTree/{projectId}", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_GATEWAY_LIST)
    @ResponseBody
    public List<Device> getDeviceSearchTree(@PathVariable String projectId) throws ThingsboardException {
        try {
            TenantId tenantId = getTenantId();
            List<Device> list = checkNotNull(deviceService.findByProjectId(projectId, getTenantId()));
            // 查询密钥
            List<DeviceCredentials> deviceCredentialsList = deviceCredentialsService.findDeviceCredentialsByTenantId(tenantId);
            Map<DeviceId, DeviceCredentials> deviceCredentialsMap = new HashMap<>();
            deviceCredentialsList.forEach(deviceCredentials -> {
                deviceCredentialsMap.put(deviceCredentials.getDeviceId(), deviceCredentials);
            });
            list = list.stream().filter(device -> {
                if (device.getAdditionalInfo() != null) {
                    JSONObject additionalInfo = JSON.parseObject(device.getAdditionalInfo().asText());
                    if (additionalInfo != null && additionalInfo.containsKey("gateway") && additionalInfo.getBoolean("gateway")) {
                        return true;
                    }
                }
                DeviceCredentials credentials = deviceCredentialsMap.get(device.getId());
                if (credentials != null) {
                    device.setCredentialsId(credentials.getCredentialsId());
                }
                return false;
            }).collect(Collectors.toList());
            list.forEach(device -> {
                device.setDevices(deviceService.findDeviceByGateWayId(device.getId()).stream().peek(childDevice -> {
                    DeviceCredentials credentials = deviceCredentialsMap.get(childDevice.getId());
                    if (credentials != null) {
                        childDevice.setCredentialsId(credentials.getCredentialsId());
                    }
                }).collect(Collectors.toList()));
            });
            return list;
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    /**
     * 根据projectId和设备类型获取设备
     *
     * @return
     * @throws ThingsboardException
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/tenant/deviceSearchTree/{projectId}/{type}", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_GATEWAY_LIST)
    @ResponseBody
    public PageData getDeviceSearchTree(@PathVariable String projectId, @PathVariable String type,
                                        @RequestParam(required = false, defaultValue = "") String name,
                                        @RequestParam(required = false, defaultValue = "1") Integer page,
                                        @RequestParam(required = false, defaultValue = "20") Integer size) throws ThingsboardException {
        try {
            TenantId tenantId = getTenantId();
            // 查询密钥
            List<DeviceCredentials> deviceCredentialsList = deviceCredentialsService.findDeviceCredentialsByTenantId(tenantId);
            Map<DeviceId, DeviceCredentials> deviceCredentialsMap = new HashMap<>();
            deviceCredentialsList.forEach(deviceCredentials -> {
                deviceCredentialsMap.put(deviceCredentials.getDeviceId(), deviceCredentials);
            });
            List<Device> list = null;
            if (org.apache.commons.lang3.StringUtils.isNotBlank(name)) {
                list = deviceService.findByProjectIdAndTypeAndName(projectId, type, name);
            } else {
                list = deviceService.findByProjectIdAndType(projectId, type);
            }
            list = list.stream().filter(device -> {
                DeviceCredentials credentials = deviceCredentialsMap.get(device.getId());
                if (credentials != null) {
                    device.setCredentialsId(credentials.getCredentialsId());
                }
                if (device.getAdditionalInfo() != null) {
                    JSONObject additionalInfo = JSON.parseObject(device.getAdditionalInfo().asText());
                    if ((additionalInfo != null && additionalInfo.containsKey("gateway") && additionalInfo.getBoolean("gateway")) || device.getGateWayId() == null) {
                        return true;
                    }
                }
                return false;
            }).collect(Collectors.toList());
            list.forEach(device -> {
                device.setDevices(deviceService.findDeviceByGateWayId(device.getId()).stream().peek(childDevice -> {
                    DeviceCredentials credentials = deviceCredentialsMap.get(childDevice.getId());
                    if (credentials != null) {
                        childDevice.setCredentialsId(credentials.getCredentialsId());
                    }
                }).collect(Collectors.toList()));
            });

            List<Device> dataList = new ArrayList<>();
            page = page - 1;
            if (!list.isEmpty()) {
                int dataSize = list.size();
                List<Device> pageResultList = new ArrayList<>();
                if (dataSize <= size) {
                    pageResultList = list;
                } else {
                    int start = page * size;
                    int end = ((page + 1) * size) - 1;
                    int i = 0;
                    for (int j = 0; j < list.size(); j++) {
                        if (j > end) {
                            break;
                        }
                        if (i >= start && i <= end) {
                            pageResultList.add(list.get(j));
                        }
                        i++;
                    }
                }

                return new PageData<>(list.size(), pageResultList);
            }

            return new PageData<>(0, dataList);
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    /**
     * 获取所有的设备
     *
     * @return
     * @throws ThingsboardException
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "cloud/deviceList", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_GATEWAY_LIST)
    @ResponseBody
    public List<Device> getCloudDeviceList() {
        return deviceService.findCloudDeviceList();
    }


    /**
     * 复制设备
     *
     * @return
     * @throws ThingsboardException
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/copy/gateway/{gatewayId}/{projectId}", method = RequestMethod.POST)
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_DEVICE_COPY_GATEWAY)
    @ResponseBody
    public Object copyGateway(@PathVariable String gatewayId, @PathVariable String projectId) throws ThingsboardException {
        JSONObject result = new JSONObject();
        String suffix = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(new Date());
        try {
            // 复制主机
            DeviceId gateWayId = new DeviceId(UUIDConverter.fromString(gatewayId));
            Device gateway = deviceService.findDeviceById(gateWayId);
            gateway.setName(gateway.getName() + " 副本-" + suffix);
            gateway.setId(null);
            gateway.setProjectId(projectId);
            JsonNode additionalInfo = gateway.getAdditionalInfo();
            if (additionalInfo != null) {
                JSONObject obj = JSON.parseObject(additionalInfo.asText());
                obj.put("name", gateway.getName());
                gateway.setAdditionalInfo(new TextNode(obj.toJSONString()));
            }
            gateway = this.saveDevice(gateway, false);

            // 复制从机
            List<Device> sourceDeviceList = deviceService.findDeviceByGateWayId(gateWayId);
            for (Device device : sourceDeviceList) {
                // 保存
                device.setProp(attributesService.findNotFuture(device.getId(), DataConstants.SHARED_SCOPE, ModelConstants.PROR).getValueAsString());
                device.setAttribute(attributesService.findNotFuture(device.getId(), DataConstants.SHARED_SCOPE, ModelConstants.ATTRIBUTE).getValueAsString());
                device.setId(null);
                device.setName(device.getName() + " 副本-" + suffix);
                device.setProjectId(projectId);
                device.setGateWayId(gateway.getId());

                this.saveDevice(device, true);
            }

        } catch (Exception e) {
            throw handleException(e);
        }

        result.put("result", "操作成功");
        return result;
    }

    @GetMapping("/device/findByDeviceTypeName")
    public List<Device> findByDeviceTypeName(String deviceTypeName) throws ThingsboardException {
        return deviceService.findByDeviceTypeName(deviceTypeName, getTenantId());
    }

    /**
     * 获取企业下设备信息(设备数量)
     *
     * @return
     */
    @GetMapping(value = "/tenantDeviceMsg")
    public TenantDeviceMsg getTenantDeviceMsg() throws ThingsboardException {
        TenantDeviceMsg tenantDeviceMsg = TenantDeviceMsg.builder()
                .gatewayAllCount(deviceService.findAllGatewayByTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId())).size())
                .gatewayOnlineCount(deviceService.findAllOnlineGatewayByTenantId(getTenantId()))
                .alarmCount(alarmService.getUnClearAlarmByTenantId(getTenantId()))
                .deviceAllCount(deviceService.findAllDeviceByTenantId(getTenantId()))
                .deviceOnlineCount(deviceService.findAllOnlineDeviceByTenantId(getTenantId()))
                .build();
        if (tenantDeviceMsg.getGatewayAllCount() != 0) {
            tenantDeviceMsg.setGatewayOnlineRate(new BigDecimal(tenantDeviceMsg.getGatewayOnlineCount()).divide(new BigDecimal(tenantDeviceMsg.getGatewayAllCount()), 2, BigDecimal.ROUND_HALF_UP));
        } else {
            tenantDeviceMsg.setGatewayOnlineRate(new BigDecimal(0));
        }
        if (tenantDeviceMsg.getDeviceAllCount() != 0) {
            tenantDeviceMsg.setDeviceOnlineRate(new BigDecimal(tenantDeviceMsg.getDeviceOnlineCount()).divide(new BigDecimal(tenantDeviceMsg.getDeviceAllCount()), 2, BigDecimal.ROUND_HALF_UP));
        } else {
            tenantDeviceMsg.setDeviceOnlineRate(new BigDecimal(0));
        }
        return tenantDeviceMsg;
    }

    /**
     * 获取所有设备
     */
    @GetMapping("getAllDevice")
    public List<Device> getAllDevice() {
        return deviceService.findAll();
    }

    /**
     * 查询指定项目下的所有设备的所有变量信息（包含当前值）
     */
    @GetMapping("device/property/data")
    public List getPropertyDataByDeviceList(@RequestParam(required = false) String projectId,
                                            @RequestParam(required = false) String propertyType,
                                            @RequestParam(required = false) String deviceId) throws ThingsboardException {
        List<String> propertyTypeFilter = new ArrayList<>();
        if (StringUtils.checkNotNull(propertyType)) {
            propertyTypeFilter.addAll(Arrays.asList(propertyType.split(",")));
        }

        // 查询设备列表
        List<Device> deviceList = new ArrayList<>();
        if (StringUtils.checkNotNull(projectId)) {
            deviceList = this.getDeviceByProject(projectId);
        }

        if (StringUtils.checkNotNull(deviceId)) {
            deviceList = deviceList.stream()
                    .filter(device -> UUIDConverter.fromTimeUUID(device.getUuidId()).equals(deviceId))
                    .collect(Collectors.toList());
        }

        if (propertyTypeFilter.size() > 0) {
            deviceList = deviceList.stream()
                    .filter(device -> propertyTypeFilter.contains(device.getDeviceTypeName()))
                    .collect(Collectors.toList());
        }

        // 查询每个设备的变量列表信息
        for (Device device : deviceList) {
            List<DeviceFullData> deviceFullData = deviceService.getDeviceFullData(device.getTenantId(), device.getId(), null);

            if (deviceFullData != null) {
                // 判断是否需要显示远程控制按钮
                List<DeviceFullData> controlPropertyList = deviceFullData.stream().filter(data -> {
                    String dataPropertyType = data.getPropertyType();
                    if ("2".equals(dataPropertyType) || "3".equals(dataPropertyType) || "4".equals(dataPropertyType)) {
                        return true;
                    }
                    return false;
                }).collect(Collectors.toList());
                device.setControl(controlPropertyList.size() > 0);

            }
            device.setPropertyDataList(deviceFullData);
        }

        return deviceList;
    }


    @GetMapping({"device/getPartitionDevice"})
    public IstarResponse getPartitionDevice(PartitionMountRequest request) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(this.getTenantId().getId());
        request.setTenantId(tenantId);
        return IstarResponse.ok(this.deviceService.getPartitionDevice(request));
    }
}
