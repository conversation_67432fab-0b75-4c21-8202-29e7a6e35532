package org.thingsboard.server.dao.model.sql;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 门禁视频关联实体
 * 
 * <AUTHOR>
 * @date 2024
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_access_control_video")
public class AccessControlVideo {

    /**
     * 主键ID
     */
    @TableId
    private String id;

    /**
     * 门禁ID
     */
    @TableField("access_control_id")
    private String accessControlId;

    /**
     * 视频ID
     */
    @TableField("video_id")
    private String videoId;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 关联的视频信息（非数据库字段）
     */
    @TableField(exist = false)
    private VideoEntity video;

    /**
     * 关联的门禁信息（非数据库字段）
     */
    @TableField(exist = false)
    private AccessControl accessControl;

    /**
     * 构造函数 - 用于快速创建关联
     * 
     * @param accessControlId 门禁ID
     * @param videoId 视频ID
     * @param tenantId 租户ID
     */
    public AccessControlVideo(String accessControlId, String videoId, String tenantId) {
        this.accessControlId = accessControlId;
        this.videoId = videoId;
        this.tenantId = tenantId;
        this.createTime = new Date();
    }

    /**
     * 检查关联是否有效
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        return accessControlId != null && !accessControlId.trim().isEmpty()
                && videoId != null && !videoId.trim().isEmpty()
                && tenantId != null && !tenantId.trim().isEmpty();
    }

    @Override
    public String toString() {
        return "AccessControlVideo{" +
                "id='" + id + '\'' +
                ", accessControlId='" + accessControlId + '\'' +
                ", videoId='" + videoId + '\'' +
                ", tenantId='" + tenantId + '\'' +
                ", createTime=" + createTime +
                '}';
    }
}
