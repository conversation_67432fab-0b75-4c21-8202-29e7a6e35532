<!-- 事件总览 -->
<template>
  <RightDrawerMap
    ref="refMap"
    title="事件总览"
    :detail-max-min="true"
    :hide-right-drawer="true"
    :hide-detail-close="true"
    :map-config="mapConfig"
    @map-loaded="onMapLoaded"
  >
    <template #detail-header>
      <span>事件总览</span>
    </template>
    <template #detail-default>
      <div class="detail-wrapper">
        <CardSearch ref="refSearch" :config="SearchConfig" style="margin-bottom: 8px;"></CardSearch>
        <div class="detail-table">
          <CardTable ref="refTable" :config="TableConfig"></CardTable>
        </div>
      </div>
    </template>
  </RightDrawerMap>

  <!-- 事件表单弹窗 -->
  <EventOverviewForm
    v-model="showEventForm"
    :is-edit="isEditMode"
    :is-view="isViewMode"
    :edit-data="editEventData"
    @submit="handleEventSubmit"
  />

  <!-- 审核弹窗 -->
  <ReviewDialog
    v-model="showReviewDialog"
    :event-data="currentReviewEvent"
    @submit="handleReviewSubmit"
  />
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, shallowRef, computed } from 'vue'
import { Refresh, Plus, Edit, Delete } from '@element-plus/icons-vue'
import { SLMessage, SLConfirm, SLPrompt } from '@/utils/Message'
import { GetEventOverviewPage, CreateEventOverview, UpdateEventOverview, DeleteEventOverview, BatchDeleteEventOverview } from '@/api/workorder/eventOverview'
import request from '@/plugins/axios'
import {
  WorkOrderStatusRecord
} from './config'
import { formatterDate } from '@/utils/GlobalHelper'
import RightDrawerMap from '@/views/arcMap/components/common/RightDrawerMap.vue'
import EventOverviewForm from './components/EventOverviewForm.vue'
import ReviewDialog from './components/ReviewDialog.vue'
import Graphic from '@arcgis/core/Graphic'
import Point from '@arcgis/core/geometry/Point'
import SimpleMarkerSymbol from '@arcgis/core/symbols/SimpleMarkerSymbol'
import PictureMarkerSymbol from '@arcgis/core/symbols/PictureMarkerSymbol'
import PopupTemplate from '@arcgis/core/PopupTemplate'
import { status } from 'nprogress'
const refSearch = ref<ICardSearchIns>()
const refTable = ref<ICardTableIns>()
const refMap = ref<InstanceType<typeof RightDrawerMap>>()

// 表单相关
const showEventForm = ref(false)
const isEditMode = ref(false)
const isViewMode = ref(false)
const editEventData = ref<any>(null)

// 审核弹窗相关
const showReviewDialog = ref(false)
const currentReviewEvent = ref(null)

// 批量操作相关
const selectedRows = ref<any[]>([])

// 地图相关
let mapView: __esri.MapView | null = null

// 地图配置
const mapConfig = reactive<IFormGisConfig>({
  defaultBaseMap: 'img_w',
  defaultCenter: [95.787329, 40.516879], // 瓜州县中心坐标
  zoom: 10
})

// 查询配置
const SearchConfig = reactive<ISearch>({
  filters: [
    {
      field: 'date',
      label: '创建时间',
      type: 'daterange'
    },
    {
      field: 'title',
      label: '名称',
      type: 'input',
      placeholder: '请输入事件名称'
    },
    {
      field: 'type',
      label: '类型',
      type: 'cascader',
      options: [],
      props: {
        value: 'id',
        label: 'name',
        children: 'children',
        emitPath: true, // 返回完整路径用于反显
        checkStrictly: false // 必须选择到叶子节点
      }
    },
    {
      field: 'status',
      label: '状态',
      type: 'select',
      options: []
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          type: 'primary',
          text: '查询',
          icon: 'iconfont icon-chaxun',
          click: () => refreshData()
        },
        {
          perm: true,
          type: 'default',
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => resetForm()
        },
        {
          perm: true,
          type: 'danger',
          text: '批量删除',
          disabled: () => !TableConfig.selectList?.length,
          svgIcon: shallowRef(Delete),
          click: () => handleBatchDelete()
        },
        {
          perm: true,
          type: 'success',
          text: '新增事件',
          svgIcon: shallowRef(Plus),
          click: () => handleCreate()
        }
      ]
    }
  ],
  handleSearch: () => refreshData(),
  defaultParams: {
    date: [
      moment().subtract(7, 'day').format(formatterDate),
      moment().format(formatterDate)
    ]
  }
})

// 表格配置
const TableConfig = reactive<ICardTable>({
  loading: false,
  dataList: [],
  selectList: [],
  handleSelectChange: (selection: any[]) => {
    TableConfig.selectList = selection
    selectedRows.value = selection
  },
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  selectable: (row: any) => {
    // 只有待审核、已分派、处理中状态的事件可以删除
    return ['PENDING_REVIEW', 'ASSIGNED', 'PROCESSING'].includes(row.status)
  },
  columns: [
    {
      minWidth: 150,
      prop: 'title',
      label: '事件标题',
      showOverflowTooltip: true
    },
    {
      minWidth: 150,
      prop: 'typeName',
      label: '类型'
    },
    {
      minWidth: 200,
      prop: 'address',
      label: '发生地点',
      showOverflowTooltip: true,
      cellStyle: (row: any) => {
        // 所有地址都显示为可点击的链接样式（用于测试）
        return {
          color: '#409eff',
          cursor: 'pointer',
          textDecoration: 'underline'
        }
      },
      handleClick: (row: any) => {
        // 首先进行地图定位
        handleAddressClick(row.address, row.coordinate)
        // 定位完成后，将地图设置为中等大小显示（占屏幕50%）
        setTimeout(() => {
          refMap.value?.toggleCustomDetailMaxmin('normal')
        }, 1000)
      }
    },
    {
      minWidth: 120,
      prop: 'status',
      label: '状态',
      tag: true,
      tagColor: (row: any): string => {
        const statusColorMap: Record<string, string> = {
          // 新的审核流程状态
          'PENDING_REVIEW': '#e6a23c',  // 待审核 - 橙色
          'ASSIGNED': '#409eff',        // 已分派 - 蓝色
          'PROCESSING': '#67c23a',      // 处理中 - 绿色
          'COMPLETED': '#67c23a',       // 已完成 - 绿色
          'REJECTED': '#f56c6c',        // 已驳回 - 红色
          // 保留原有状态的兼容性
          'PENDING': '#e6a23c',
          'ASSIGN': '#409eff',
          'RESOLVING': '#67c23a',
          'ARRIVING': '#67c23a',
          'SUBMIT': '#909399',
          'REVIEW': '#909399',
          'CHARGEBACK_REVIEW': '#909399',
          'HANDOVER_REVIEW': '#909399',
          'APPROVED': '#67c23a',
          'COMPLETE': '#67c23a',
          'TERMINATED': '#f56c6c',
          'CHARGEBACK': '#f56c6c'
        }
        return statusColorMap[row.status] || '#909399'
      },
      formatter: (row: any) => {
        return WorkOrderStatusRecord[row.status as keyof typeof WorkOrderStatusRecord] || row.statusName || '-'
      }
    },
    {
      minWidth: 100,
      prop: 'organizerName',
      label: '创建人'
    },
    {
      minWidth: 160,
      prop: 'createTime',
      label: '创建时间',
      formatter: (row: any) => {
        return row.createTime ? moment(row.createTime).format('YYYY-MM-DD HH:mm:ss') : '-'
      }
    }
  ],
  operations: [
    {
      perm: true,
      text: '查看',
      type: 'success',
      isTextBtn: true,
      click: (row: any) => handleView(row)
    },
    {
      perm: true,
      text: '编辑',
      type: 'primary',
      isTextBtn: true,
      click: (row: any) => handleEdit(row),
      hide: (row: any) => row.status === 'COMPLETED' || row.status === 'PROCESSING' || row.status === 'REJECTED'
    },
     {
      perm: true,
      text: '再次发起',
      type: 'primary',
      isTextBtn: true,
      click: (row: any) => handleEventSubmitAgain(row),
      hide: (row: any) => row.status !== 'REJECTED'
    },
    {
      perm: true,
      text: '审核',
      type: 'primary',
      isTextBtn: true,
      click: (row: any) => handleReview(row),
      hide: (row: any) => row.status !== 'PENDING_REVIEW'
    }
  ]
})

// 刷新数据
const refreshData = async () => {
  TableConfig.loading = true
  try {
    const query = refSearch.value?.queryParams || {}
    const [fromTime, toTime] = query.date?.length === 2
      ? [
          moment(query.date[0], formatterDate).valueOf(),
          moment(query.date[1], formatterDate).endOf('D').valueOf()
        ]
      : [
          moment().subtract(7, 'day').startOf('D').valueOf(),
          moment().endOf('D').valueOf()
        ]

    const params: any = {
      page: TableConfig.pagination.page || 1,
      size: TableConfig.pagination.limit || 20,
      ...query,
      fromTime,
      toTime
    }
    delete params.date

    // 处理type参数，如果是数组则只取最后一个值（子级ID）
    if (params.type && Array.isArray(params.type)) {
      params.type = params.type[params.type.length - 1]
    }

    const res = await GetEventOverviewPage(params)
    const data = res.data?.data
    TableConfig.dataList = data.data || []
    TableConfig.pagination.total = data.total || 0
  } catch (error) {
    SLMessage.error('查询失败')
  }
  TableConfig.loading = false
}

// 重置表单
const resetForm = () => {
  refSearch.value?.resetForm()
  refreshData()
}

// 处理地址点击 - 在地图上显示事件位置
const handleAddressClick = (address: string, coordinate?: string) => {

  if (!coordinate) {
    SLMessage.warning('该事件暂无位置信息')
    return
  }

  if (!mapView) {
    SLMessage.warning('地图尚未加载完成，请稍后重试')
    return
  }

  try {
    const coords = coordinate.split(',')
    if (coords.length !== 2) {
      SLMessage.error('坐标格式错误')
      return
    }

    const longitude = parseFloat(coords[0])
    const latitude = parseFloat(coords[1])

    if (isNaN(longitude) || isNaN(latitude)) {
      SLMessage.error('坐标数据无效')
      return
    }

    // 清除之前的标记
    mapView.graphics.removeAll()

    // 创建标记点 - 参考lookBoard的实现
    const point = new Point({
      longitude: longitude,
      latitude: latitude,
      spatialReference: mapView.spatialReference
    })

    const markerSymbol = new SimpleMarkerSymbol({
      color: [226, 119, 40],
      outline: {
        color: [255, 255, 255],
        width: 2
      },
      size: 14
    })

    const popupTemplate = new PopupTemplate({
      title: '事件位置',
      content: `
        <div style="padding: 8px;">
          <p><strong>地址：</strong>${address}</p>
          <p><strong>经度：</strong>${longitude}</p>
          <p><strong>纬度：</strong>${latitude}</p>
        </div>
      `
    })

    const graphic = new Graphic({
      geometry: point,
      symbol: markerSymbol,
      popupTemplate: popupTemplate,
      attributes: {
        address: address,
        longitude: longitude,
        latitude: latitude
      }
    })

    mapView.graphics.add(graphic)

    // 定位到该点 - 使用goTo方法
    mapView.goTo({
      target: point,
      zoom: 16
    }).then(() => {
    }).catch((error) => {
      console.error('地图定位失败:', error)
    })

    SLMessage.success('已定位到事件位置')
  } catch (error) {
    console.error('地图定位失败:', error)
    SLMessage.error('地图定位失败')
  }
}

// 新建事件
const handleCreate = () => {
  isEditMode.value = false
  isViewMode.value = false
  editEventData.value = null
  showEventForm.value = true
}

// 查看事件
const handleView = (row: any) => {
  isEditMode.value = false
  isViewMode.value = true
  editEventData.value = { ...row }
  showEventForm.value = true
}

// 编辑事件
const handleEdit = (row: any) => {
  isEditMode.value = true
  isViewMode.value = false
  editEventData.value = { ...row }
  showEventForm.value = true
}

// 再次发起事件（被驳回后重新提交）
const handleEventSubmitAgain = (row: any) => {
  isEditMode.value = true
  isViewMode.value = false

  editEventData.value = { ...row }
  showEventForm.value = true
}

// 处理表单提交
const handleEventSubmit = () => {
  showEventForm.value = false
  refreshData()
}






// 打开审核弹窗
const handleReview = (row: any) => {
  currentReviewEvent.value = row
  showReviewDialog.value = true
}

// 审核提交完成
const handleReviewSubmit = () => {
  showReviewDialog.value = false
  refreshData()
}

// 批量删除事件
const handleBatchDelete = async () => {
  try {
    if (!TableConfig.selectList || TableConfig.selectList.length === 0) {
      SLMessage.warning('请选择要删除的事件')
      return
    }

    // 检查选中的事件状态
    const invalidRows = TableConfig.selectList.filter(row =>
      !['PENDING_REVIEW', 'ASSIGNED', 'PROCESSING'].includes(row.status)
    )

    if (invalidRows.length > 0) {
      SLMessage.warning('只能删除待审核、已分派、处理中状态的事件')
      return
    }

    await SLConfirm(`确认删除选中的 ${TableConfig.selectList.length} 个事件吗？`, '批量删除')

    // 批量删除API调用
    const ids = TableConfig.selectList.map(row => row.id)
    const result = await BatchDeleteEventOverview(ids)

    // 按照标准的响应判断方式
    if (result.code === 200 || result.data?.code === 200) {

      const  successCount = result.data.data.successCount
      const  totalCount = result.data.data.totalCount
      const  failedCount = result.data.data.failedCount


      if (successCount === totalCount) {
        SLMessage.success(`成功删除 ${successCount} 个事件`)
      } else {
        SLMessage.warning(`成功删除 ${successCount} 个事件，失败 ${failedCount} 个`)
      }
    } else {
      SLMessage.error(result.data?.msg || result.message || '批量删除失败')
      return
    }

    // 清空选择并刷新数据
    TableConfig.selectList = []
    selectedRows.value = []
    refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      SLMessage.error('批量删除操作失败')
    }
  }
}

// 初始化选项
const initOptions = async () => {
  // 初始化状态选项 - 根据新的审核流程
  const statusField = SearchConfig.filters?.find(f => f.field === 'status') as any
  if (statusField) {
    statusField.options = [
      { label: '待审核', value: 'PENDING_REVIEW' },
      { label: '已分派', value: 'ASSIGNED' },
      { label: '处理中', value: 'PROCESSING' },
      { label: '已完成', value: 'COMPLETED' },
      { label: '已驳回', value: 'REJECTED' }
    ]
  }

  // 初始化类型选项 - 从API获取级联数据
  const typeField = SearchConfig.filters?.find(f => f.field === 'type') as any
  if (typeField) {
    try {
      const res = await request({
        url: '/api/workOrderType/list',
        method: 'get',
        params: { status: 1 }
      })

      if (res.data?.data) {
        // 只获取父级类型（parentId为"0"的是父级类型）
        const parentTypes = res.data.data.filter((item: any) =>
          item.parentId === "0" || item.parentId === 0 || !item.parentId
        )

        // 构建级联选择器的数据结构
        typeField.options = parentTypes.map((parent: any) => ({
          id: parent.id,
          name: parent.name,
          children: parent.children || []
        }))


      }
    } catch (error) {
      console.error('获取工单类型失败:', error)
      SLMessage.error('获取工单类型失败')
    }
  }
}

// 地图加载完成回调
const onMapLoaded = (view: __esri.MapView) => {
  mapView = view

  // 延迟一下确保组件完全加载
  setTimeout(() => {
    // 默认打开详情面板并设置为最大化状态（列表优先显示，参考TaskDispatch）
    refMap.value?.toggleCustomDetail(true)
    refMap.value?.toggleCustomDetailMaxmin('max')
  }, 100)
}

onMounted(async () => {
  await initOptions()
  refreshData()
})
</script>

<style lang="scss" scoped>
.detail-wrapper {
  height: 100%;

  .detail-table {
    height: calc(100% - 50px);
  }
}

:deep(.address-link) {
  color: #409eff;
  cursor: pointer;
  text-decoration: underline;

  &:hover {
    color: #66b1ff;
  }
}

.event-form {
  .map-selector {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;
    width: 100%;

    .map-selector-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background-color: #f5f7fa;
      border-bottom: 1px solid #dcdfe6;

      span {
        font-size: 14px;
        color: #606266;
      }
    }

    .map-container {
      height: 350px;
      width: 100%;

      :deep(.location-map) {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
