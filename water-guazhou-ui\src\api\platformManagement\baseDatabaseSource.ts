import { request } from '@/plugins/axios'

// 获取数据库多数据源列表
export function getBaseDatabaseSourceList(params: any) {
  return request({
    url: '/api/base/database/source/list',
    method: 'get',
    params
  })
}

// 获取数据库多数据源详情
export function getBaseDatabaseSourceDetail(id: string) {
  return request({
    url: '/api/base/database/source/getDetail',
    method: 'get',
    params: { id }
  })
}

// 新增数据库多数据源配置
export function addBaseDatabaseSource(data: any) {
  return request({
    url: '/api/base/database/source/add',
    method: 'post',
    data
  })
}

// 修改数据库多数据源配置
export function editBaseDatabaseSource(data: any) {
  return request({
    url: '/api/base/database/source/edit',
    method: 'post',
    data
  })
}

// 删除数据库多数据源配置
export function deleteBaseDatabaseSource(ids: string[]) {
  return request({
    url: '/api/base/database/source/deleteIds',
    method: 'delete',
    data: ids
  })
} 