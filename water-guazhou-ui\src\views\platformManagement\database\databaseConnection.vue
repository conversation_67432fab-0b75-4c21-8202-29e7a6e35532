<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      class="card-table"
      :config="TableConfig"
    ></CardTable>
    <DialogForm
      ref="refDialogForm"
      :config="DialogFormConfig"
    ></DialogForm>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue'
import { 
  getDatabaseConnectionList,
  addDatabaseConnection,
  editDatabaseConnection,
  deleteDatabaseConnection,
  getDatabaseConnectionDetail 
} from '@/api/platformManagement/baseDatabaseConnection'
import { SLConfirm, SLMessage } from '@/utils/Message'
import dayjs from 'dayjs'

const refSearch = ref()
const refDialogForm = ref()

// 端口号验证规则
const portValidator = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入端口号'))
    return
  }
  const port = Number(value)
  if (isNaN(port) || port < 1 || port > 65535) {
    callback(new Error('端口号必须为1-65535之间的数字'))
  } else {
    callback()
  }
}

const SearchConfig = reactive({
  labelWidth: '100px',
  filters: [
    { 
      type: 'input', 
      label: '方案名称', 
      field: 'name', 
      placeholder: '请输入方案名称',
      onChange: () => refreshData() 
    },
    {
      type: 'btn-group',
      btns: [
        { type: 'primary', perm: true, text: '查询', click: () => refreshData() },
        { perm: true, type: 'primary', text: '新增', click: () => handleAdd() },
        { perm: true, type: 'danger', text: '批量删除', click: () => handleDelete() }
      ]
    }
  ],
  defaultParams: {}
})

const TableConfig = reactive({
  columns: [
    { label: '主键ID', prop: 'id' },
    { label: '方案名称', prop: 'name' },
    { label: '数据库类型', prop: 'dbType' },
    { label: '服务器地址', prop: 'dbHost' },
    { label: '端口号', prop: 'dbPort' },
    { 
      label: '创建时间', 
      prop: 'createdAt',
      render: (row) => dayjs(row.createdAt).format('YYYY-MM-DD HH:mm')
    },
    { 
      label: '更新时间',
      prop: 'updatedAt',
      render: (row) => dayjs(row.updatedAt).format('YYYY-MM-DD HH:mm')
    }
  ],
  dataList: [],
  operations: [
    {
      perm: true,
      type: 'primary',
      isTextBtn: true,
      text: '查看详情',
      click: (row) => handleDetail(row)
    },
    {
      perm: true,
      type: 'primary',
      isTextBtn: true,
      text: '编辑',
      click: (row) => handleAdd(row)
    },
    {
      perm: true,
      type: 'danger',
      isTextBtn: true,
      text: '删除',
      click: (row) => handleDelete(row)
    }
  ],
  pagination: {
    total: 0,
    page: 1,
    align: 'right',
    limit: 20,
    handlePage: (page) => {
      TableConfig.pagination.page = page
      refreshData()
    },
    handleSize: (size) => {
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  handleSelectChange: (rows) => {
    TableConfig.selectList = rows || []
  }
})

const DialogFormConfig = reactive({
  title: '新增数据库连接',
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '方案名称',
          field: 'name',
          rules: [{ required: true, message: '请输入方案名称' }]
        },
        {
          type: 'select',
          label: '数据库类型',
          field: 'dbType',
          options: [
            { label: 'MySQL', value: 'MySQL' },
            { label: 'Oracle', value: 'Oracle' },
            { label: 'PostgreSQL', value: 'PostgreSQL' },
            { label: 'SQL Server', value: 'SQL Server' }
          ],
          rules: [{ required: true, message: '请选择数据库类型' }]
        },
        {
          type: 'input',
          label: '服务器地址',
          field: 'dbHost',
          rules: [{ required: true, message: '请输入服务器地址' }]
        },
        {
          type: 'input',
          label: '端口号',
          field: 'dbPort',
          rules: [
            { required: true, message: '请输入端口号' },
            { validator: portValidator, trigger: 'blur' }
          ]
        },
        {
          type: 'input',
          label: '数据库名称',
          field: 'dbName',
          rules: [{ required: true, message: '请输入数据库名称' }]
        },
        {
          type: 'input',
          label: '用户名',
          field: 'dbUser',
          rules: [{ required: true, message: '请输入用户名' }]
        },
        {
          type: 'password',
          label: '密码',
          field: 'dbPwd',
          rules: [{ required: true, message: '请输入密码' }],
          showPassword: true
        },
        {
          type: 'textarea',
          label: '初始化SQL脚本',
          field: 'initScriptDb',
          placeholder: '请输入数据库初始化SQL脚本',
          inputStyle: { height: '100px' }
        },
        {
          type: 'textarea',
          label: '方案描述',
          field: 'description',
          placeholder: '请输入方案描述'
        }
      ]
    }
  ],
  labelPosition: 'top',
  defaultValue: {},
  dialogWidth: 600,
  draggable: true,
  showSubmit: true,
  showCancel: true,
  cancelText: '取消',
  submitText: '确定',
  submit: async (params) => {
    try {
      // 密码加密处理（根据实际加密方法调整）
      console.log('提交参数（加密前）:', params);
      const encryptedParams = {
        ...params,
        dbPwd: btoa(params.dbPwd) // 示例使用base64加密，根据实际需求替换
      }
      console.log('提交参数（加密后）:', encryptedParams);
  
      if (params.id) {
        await editDatabaseConnection(encryptedParams)
        SLMessage.success('修改成功')
      } else {
        await addDatabaseConnection(encryptedParams)
        SLMessage.success('新增成功')
      }
      refDialogForm.value?.closeDialog()
      refreshData()
    } catch (error) {
      SLMessage.error('操作失败')
    }
  }
})

// 重置对话框配置
const resetDialogConfig = () => {
  // 重置所有表单字段为可编辑状态
  DialogFormConfig.group[0].fields.forEach(field => {
    field.disabled = false
    field.readonly = false
  })

  // 恢复默认按钮配置
  DialogFormConfig.showSubmit = true
  DialogFormConfig.showCancel = true
  DialogFormConfig.cancelText = '取消'
  DialogFormConfig.submitText = '确定'

  // 恢复提交函数
  DialogFormConfig.submit = async (params) => {
    try {
      if (params.id) {
        await editDatabaseConnection(params)
        SLMessage.success('修改成功')
      } else {
        await addDatabaseConnection(params)
        SLMessage.success('新增成功')
      }
      refDialogForm.value?.closeDialog()
      refreshData()
    } catch (error) {
      SLMessage.error('操作失败')
    }
  }

  // 清除自定义按钮配置
  DialogFormConfig.footerBtns = undefined
}

// 新增/编辑
const handleAdd = (row) => {
  resetDialogConfig()
  DialogFormConfig.title = row ? '编辑数据库连接' : '新增数据库连接'
  DialogFormConfig.defaultValue = { ...(row || {}) }
  refDialogForm.value?.openDialog()
}

// 查看详情
const handleDetail = async (row) => {
  try {
    const res = await getDatabaseConnectionDetail(row.id)
    let detailData = res.data?.data || res.data || res

    // 密码解密显示（根据实际加密方式调整）
    if (detailData.dbPwd) {
      detailData = {
        ...detailData,
        dbPwd: atob(detailData.dbPwd) // 示例使用base64解密
      }
    }

    resetDialogConfig()
    DialogFormConfig.title = '数据库连接详情'
    DialogFormConfig.defaultValue = { ...detailData }
    DialogFormConfig.group[0].fields.forEach(field => {
      field.disabled = true
      if (field.type === 'password') field.showPassword = false
    })
    DialogFormConfig.showSubmit = false
    DialogFormConfig.cancelText = '关闭'
    refDialogForm.value?.openDialog()
  } catch (error) {
    SLMessage.error('获取详情失败')
  }
}

// 删除
const handleDelete = (row) => {
  SLConfirm('确定删除？', '删除提示')
    .then(async () => {
      const ids = row ? [row.id] : TableConfig.selectList?.map(item => item.id) || []
      if (!ids.length) {
        SLMessage.warning('请选择要删除的数据')
        return
      }
      await deleteDatabaseConnection(ids)
      SLMessage.success('删除成功')
      refreshData()
    })
    .catch(() => {
      // 取消删除
    })
}

// 刷新数据
const refreshData = async () => {
  try {
    const res = await getDatabaseConnectionList({
      page: TableConfig.pagination.page,
      size: TableConfig.pagination.limit,
      ...(refSearch.value?.queryParams || {})
    })

    // 统一数据结构处理
    const responseData = res.data?.data || res.data || res
    TableConfig.dataList = responseData.records || responseData
    TableConfig.pagination.total = responseData.total || responseData.length || 0

    // 日期字段格式化
    TableConfig.dataList.forEach(item => {
      item.createdAt = dayjs(item.createdAt).format('YYYY-MM-DD HH:mm')
      item.updatedAt = dayjs(item.updatedAt).format('YYYY-MM-DD HH:mm')
    })

  } catch (error) {
    SLMessage.error('数据加载失败')
  }
}

onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-table {
  flex: 1;
  margin-top: 16px;
}
</style>