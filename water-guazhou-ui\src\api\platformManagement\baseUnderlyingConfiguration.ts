import { request } from '@/plugins/axios'

// 获取基础配置列表
export function getUnderlyingConfigList(params: any) {
  return request({
    url: '/api/base/underlying/configuration/list',
    method: 'get',
    params
  })
}

// 获取基础配置详情
export function getUnderlyingConfigDetail(id: string) {
  return request({
    url: '/api/base/underlying/configuration/getDetail',
    method: 'get',
    params: { id }
  })
}

// 新增基础配置配置
export function addUnderlyingConfig(data: any) {
  return request({
    url: '/api/base/underlying/configuration/add',
    method: 'post',
    data
  })
}

// 修改基础配置配置
export function editUnderlyingConfig(data: any) {
  return request({
    url: '/api/base/underlying/configuration/edit',
    method: 'post',
    data
  })
}

// 删除基础配置配置
export function deleteUnderlyingConfig(ids: string[]) {
  return request({
    url: '/api/base/underlying/configuration/deleteIds',
    method: 'delete',
    data: ids
  })
} 