package org.thingsboard.server.dao.sql.smartManagement.settings;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartManagement.settings.InspectionPlanSetting;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.settings.InspectionPlanPageRequest;

/**
 * 巡检计划配置Mapper接口
 */
@Mapper
public interface InspectionPlanMapper extends BaseMapper<InspectionPlanSetting> {

    /**
     * 分页查询巡检计划列表
     *
     * @param request 查询条件
     * @return 分页结果
     */
    IPage<InspectionPlanSetting> findByPage(InspectionPlanPageRequest request);

    /**
     * 更新巡检计划
     *
     * @param entity 实体信息
     * @return 是否成功
     */
    boolean update(InspectionPlanSetting entity);

    /**
     * 根据ID查询巡检计划
     *
     * @param id 主键ID
     * @return 巡检计划
     */
    InspectionPlanSetting findById(String id);
}
