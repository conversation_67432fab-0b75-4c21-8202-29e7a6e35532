package org.thingsboard.server.dao.deviceIcon;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.deviceIcon.DeviceIcon;
import org.thingsboard.server.dao.util.imodel.query.deviceIcon.DeviceIconBatchSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.deviceIcon.DeviceIconPageRequest;
import org.thingsboard.server.dao.util.imodel.query.deviceIcon.DeviceIconSaveRequest;

import java.util.List;

/**
 * 设备图标服务接口
 */
public interface DeviceIconService {

    /**
     * 分页查询设备图标
     */
    IPage<DeviceIcon> findByPage(DeviceIconPageRequest request);

    /**
     * 根据ID查询设备图标
     */
    DeviceIcon findById(String id);

    /**
     * 根据设备类型查询设备图标
     */
    List<DeviceIcon> findByDeviceType(String deviceType, String tenantId);

    /**
     * 根据设备类型和设备状态查询设备图标
     */
    DeviceIcon findByDeviceTypeAndStatus(String deviceType, String deviceStatus, String tenantId);

    /**
     * 保存设备图标
     */
    DeviceIcon save(DeviceIconSaveRequest request, String tenantId);

    /**
     * 批量保存设备图标
     */
    void batchSave(DeviceIconBatchSaveRequest request, String tenantId);

    /**
     * 删除设备图标
     */
    boolean delete(String id);

    /**
     * 根据设备类型删除设备图标
     */
    void deleteByDeviceType(String deviceType, String tenantId);
}
