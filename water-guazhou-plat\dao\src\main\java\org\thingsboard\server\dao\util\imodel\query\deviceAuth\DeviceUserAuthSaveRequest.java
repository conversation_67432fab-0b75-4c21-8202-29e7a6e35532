package org.thingsboard.server.dao.util.imodel.query.deviceAuth;

import lombok.Data;

/**
 * 设备用户权限关联保存请求
 */
@Data
public class DeviceUserAuthSaveRequest {
    /**
     * ID
     */
    private String id;
    
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 设备名称
     */
    private String deviceName;
    
    /**
     * 设备编码
     */
    private String deviceSerial;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 用户名称
     */
    private String userName;
    
    /**
     * 权限类型（1:查看,2:控制,3:管理）
     */
    private Integer authType;
}
