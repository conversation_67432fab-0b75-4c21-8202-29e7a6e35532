package org.thingsboard.server.dao.sql.deviceAuth;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.deviceAuth.DeviceUserAuth;
import org.thingsboard.server.dao.util.imodel.query.deviceAuth.DeviceUserAuthPageRequest;

import java.util.List;

/**
 * 设备用户权限关联Mapper接口
 */
@Mapper
public interface DeviceUserAuthMapper extends BaseMapper<DeviceUserAuth> {
    
    /**
     * 分页查询设备用户权限关联
     */
    IPage<DeviceUserAuth> findByPage(DeviceUserAuthPageRequest request);
    
    /**
     * 根据设备ID查询设备用户权限关联
     */
    List<DeviceUserAuth> findByDeviceId(@Param("deviceId") String deviceId, @Param("tenantId") String tenantId);
    
    /**
     * 根据用户ID查询设备用户权限关联
     */
    List<DeviceUserAuth> findByUserId(@Param("userId") String userId, @Param("tenantId") String tenantId);
    
    /**
     * 批量插入设备用户权限关联
     */
    void batchInsert(@Param("list") List<DeviceUserAuth> list);
    
    /**
     * 根据设备ID删除设备用户权限关联
     */
    void deleteByDeviceId(@Param("deviceId") String deviceId, @Param("tenantId") String tenantId);
}
