import { request } from '@/plugins/axios'

// 获取消息配置列表
export function getMessageConfigList(params: any) {
  return request({
    url: '/api/base/message/configuration/list',
    method: 'get',
    params
  })
}

// 获取消息配置详情
export function getMessageConfigDetail(id: string) {
  return request({
    url: '/api/base/message/configuration/getDetail',
    method: 'get',
    params: { id }
  })
}

// 新增消息配置配置
export function addMessageConfig(data: any) {
  return request({
    url: '/api/base/message/configuration/add',
    method: 'post',
    data
  })
}

// 修改消息配置配置
export function editMessageConfig(data: any) {
  return request({
    url: '/api/base/message/configuration/edit',
    method: 'post',
    data
  })
}

// 删除消息配置配置
export function deleteMessageConfig(ids: string[]) {
  return request({
    url: '/api/base/message/configuration/deleteIds',
    method: 'delete',
    data: ids
  })
} 