package org.thingsboard.server.dao.model.sql.waterPlant;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 水厂压力信息实体类
 */
@Data
@TableName("tb_water_plant_pressure")
public class WaterPlantPressure {
    /**
     * ID
     */
    @TableId
    private String id;

    /**
     * 水厂ID
     */
    private String waterPlantId;

    /**
     * 水厂名称
     */
    private String waterPlantName;

    /**
     * 设备编号
     */
    private String deviceSerial;

    /**
     * 出厂水压(MPa)
     */
    private BigDecimal pressure;

    /**
     * 数据来源(手动填报/自动采集)
     */
    private String dataSource;

    /**
     * 记录时间
     */
    private Date recordTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    @ParseUsername
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 租户ID
     */
    private String tenantId;
}
