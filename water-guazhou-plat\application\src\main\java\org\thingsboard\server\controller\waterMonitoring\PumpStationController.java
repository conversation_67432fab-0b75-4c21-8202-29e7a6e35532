package org.thingsboard.server.controller.waterMonitoring;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.dao.waterMonitoring.PumpStationService;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 泵站监控管理
 */
@Slf4j
@RestController
@RequestMapping("/api/pumpStation")
@Api(tags = "泵站监控")
public class PumpStationController extends BaseController {

    @Autowired
    private PumpStationService pumpStationService;

    // 使用现有的站点列表接口 /api/station/list

    @ApiOperation(value = "获取泵站监控数据")
    @GetMapping("/monitorData")
    public IstarResponse getMonitorData(
            @RequestParam(required = false) String stationIds,
            @RequestParam(required = false, defaultValue = "day") String timeGranularity,
            @RequestParam(required = false) Long startTime,
            @RequestParam(required = false) Long endTime,
            @RequestParam(required = false, defaultValue = "electric") String pumpType
    ) throws ThingsboardException {
        List<String> stationIdList = null;
        if (stationIds != null && !stationIds.isEmpty()) {
            stationIdList = Arrays.asList(stationIds.split(","));
        }
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(pumpStationService.getMonitorData(stationIdList, timeGranularity, startTime, endTime, pumpType, tenantId));
    }

    @ApiOperation(value = "获取泵站详情")
    @GetMapping("/detail")
    public IstarResponse getStationDetail(
            @RequestParam(required = false) String stationIds
    ) throws ThingsboardException {
        List<String> stationIdList = null;
        if (stationIds != null && !stationIds.isEmpty()) {
            stationIdList = Arrays.asList(stationIds.split(","));
        }
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(pumpStationService.getStationDetail(stationIdList, tenantId));
    }

    @ApiOperation(value = "应用泵站方案")
    @PostMapping("/applyScheme")
    public IstarResponse applyScheme(@RequestBody Map<String, Object> params) throws ThingsboardException {
        String schemeId = (String) params.get("schemeId");
        List<String> stationIds = (List<String>) params.get("stationIds");
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        pumpStationService.applyScheme(schemeId, stationIds, tenantId);
        return IstarResponse.ok();
    }
}
