package org.thingsboard.server.dao.accessControl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.AccessControlVideo;
import org.thingsboard.server.dao.model.sql.VideoEntity;
import org.thingsboard.server.dao.sql.accessControl.AccessControlVideoMapper;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 门禁视频关联服务实现
 *
 * <AUTHOR>
 * @date 2024
 */
@Slf4j
@Service
public class AccessControlVideoServiceImpl implements AccessControlVideoService {

    @Autowired
    private AccessControlVideoMapper accessControlVideoMapper;

    @Override
    public AccessControlVideo save(AccessControlVideo entity) {
        log.debug("保存门禁视频关联: {}", entity);

        if (entity.getId() == null || entity.getId().isEmpty()) {
            entity.setId(UUID.randomUUID().toString());
        }
        if (entity.getCreateTime() == null) {
            entity.setCreateTime(new Date());
        }

        accessControlVideoMapper.insert(entity);
        log.info("成功保存门禁视频关联: accessControlId={}, videoId={}",
                entity.getAccessControlId(), entity.getVideoId());

        return entity;
    }

    @Override
    @Transactional
    public List<AccessControlVideo> batchSave(String accessControlId, List<String> videoIds, String tenantId) {
        log.debug("批量保存门禁视频关联: accessControlId={}, videoIds={}, tenantId={}",
                accessControlId, videoIds, tenantId);

        // 参数验证
        if (StringUtils.isBlank(accessControlId) || StringUtils.isBlank(tenantId)) {
            throw new IllegalArgumentException("门禁ID和租户ID不能为空");
        }

        // 先删除原有关联
        int deletedCount = accessControlVideoMapper.deleteByAccessControlId(accessControlId);
        log.debug("删除原有关联数量: {}", deletedCount);

        // 保存新关联
        List<AccessControlVideo> result = new ArrayList<>();
        if (videoIds != null && !videoIds.isEmpty()) {
            for (String videoId : videoIds) {
                // 检查videoId是否为空
                if (StringUtils.isNotBlank(videoId)) {
                    AccessControlVideo entity = new AccessControlVideo();
                    entity.setId(UUID.randomUUID().toString());
                    entity.setAccessControlId(accessControlId);
                    entity.setVideoId(videoId);
                    entity.setTenantId(tenantId);
                    entity.setCreateTime(new Date());

                    accessControlVideoMapper.insert(entity);
                    result.add(entity);

                    log.debug("保存关联: accessControlId={}, videoId={}", accessControlId, videoId);
                }
            }
        }

        log.info("批量保存门禁视频关联完成: accessControlId={}, 保存数量={}", accessControlId, result.size());
        return result;
    }

    @Override
    public List<VideoEntity> findVideosByAccessControlId(String accessControlId) {
        log.debug("查询门禁关联的视频: accessControlId={}", accessControlId);

        if (StringUtils.isBlank(accessControlId)) {
            return new ArrayList<>();
        }

        List<VideoEntity> videos = accessControlVideoMapper.findVideosByAccessControlId(accessControlId);
        log.debug("查询到视频数量: {}", videos.size());

        return videos;
    }

    @Override
    public List<AccessControlVideo> findByVideoId(String videoId, String tenantId) {
        log.debug("查询视频关联的门禁: videoId={}, tenantId={}", videoId, tenantId);

        if (StringUtils.isBlank(videoId)) {
            return new ArrayList<>();
        }

        List<AccessControlVideo> relations = accessControlVideoMapper.findByVideoId(videoId, tenantId);
        log.debug("查询到关联数量: {}", relations.size());

        return relations;
    }

    @Override
    public List<AccessControlVideo> findByAccessControlId(String accessControlId, String tenantId) {
        log.debug("查询门禁的关联信息: accessControlId={}, tenantId={}", accessControlId, tenantId);

        if (StringUtils.isBlank(accessControlId)) {
            return new ArrayList<>();
        }

        List<AccessControlVideo> relations = accessControlVideoMapper.findByAccessControlId(accessControlId, tenantId);
        log.debug("查询到关联数量: {}", relations.size());

        return relations;
    }

    @Override
    public boolean delete(String id) {
        log.debug("删除门禁视频关联: id={}", id);

        if (StringUtils.isBlank(id)) {
            return false;
        }

        int result = accessControlVideoMapper.deleteById(id);
        boolean success = result > 0;

        log.info("删除门禁视频关联结果: id={}, success={}", id, success);
        return success;
    }

    @Override
    @Transactional
    public boolean deleteByAccessControlId(String accessControlId) {
        log.debug("删除门禁的所有视频关联: accessControlId={}", accessControlId);

        if (StringUtils.isBlank(accessControlId)) {
            return false;
        }

        int result = accessControlVideoMapper.deleteByAccessControlId(accessControlId);
        boolean success = result > 0;

        log.info("删除门禁视频关联结果: accessControlId={}, deletedCount={}", accessControlId, result);
        return success;
    }

    @Override
    @Transactional
    public boolean deleteByVideoId(String videoId) {
        log.debug("删除视频的所有门禁关联: videoId={}", videoId);

        if (StringUtils.isBlank(videoId)) {
            return false;
        }

        int result = accessControlVideoMapper.deleteByVideoId(videoId);
        boolean success = result > 0;

        log.info("删除视频门禁关联结果: videoId={}, deletedCount={}", videoId, result);
        return success;
    }

    @Override
    public boolean isAssociated(String accessControlId, String videoId) {
        if (StringUtils.isBlank(accessControlId) || StringUtils.isBlank(videoId)) {
            return false;
        }

        QueryWrapper<AccessControlVideo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("access_control_id", accessControlId)
                   .eq("video_id", videoId);
        Integer count = accessControlVideoMapper.selectCount(queryWrapper);
        return count != null && count > 0;
    }

    @Override
    public int getVideoCountByAccessControlId(String accessControlId) {
        if (StringUtils.isBlank(accessControlId)) {
            return 0;
        }

        QueryWrapper<AccessControlVideo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("access_control_id", accessControlId);

        Integer count = accessControlVideoMapper.selectCount(queryWrapper);
        return count != null ? count.intValue() : 0;
    }

    @Override
    public int getAccessControlCountByVideoId(String videoId) {
        if (StringUtils.isBlank(videoId)) {
            return 0;
        }

        QueryWrapper<AccessControlVideo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("video_id", videoId);

        Integer count = accessControlVideoMapper.selectCount(queryWrapper);
        return count != null ? count.intValue() : 0;
    }
}
