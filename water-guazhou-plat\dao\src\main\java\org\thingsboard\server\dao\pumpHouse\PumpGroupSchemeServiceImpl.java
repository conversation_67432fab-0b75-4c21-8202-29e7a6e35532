package org.thingsboard.server.dao.pumpHouse;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse.PumpGroupScheme;
import org.thingsboard.server.dao.sql.smartProduction.pumpHouse.PumpGroupSchemeMapper;
import org.thingsboard.server.dao.sql.smartProduction.pumpHouse.PumpHouseStorageMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse.PumpGroupSchemePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse.PumpGroupSchemeSaveRequest;

import java.util.List;

@Service
public class PumpGroupSchemeServiceImpl implements PumpGroupSchemeService {
    
    @Autowired
    private PumpGroupSchemeMapper mapper;
    
    @Autowired
    private PumpHouseStorageMapper pumpHouseStorageMapper;

    @Override
    public IPage<PumpGroupScheme> findAllConditional(PumpGroupSchemePageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public PumpGroupScheme save(PumpGroupSchemeSaveRequest entity) {
        // 在保存前，根据泵房ID获取泵房编码和名称
        if (entity.getPumpRoomId() != null) {
            String pumpRoomCode = pumpHouseStorageMapper.getPumpRoomCode(entity.getPumpRoomId());
            String pumpRoomName = pumpHouseStorageMapper.getNameById(entity.getPumpRoomId());
            
            PumpGroupScheme scheme = entity.unwrap();
            scheme.setPumpRoomCode(pumpRoomCode);
            scheme.setPumpRoomName(pumpRoomName);
            
            return QueryUtil.saveOrUpdateOneByRequest(entity, mapper);
        }
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper);
    }

    @Override
    public boolean update(PumpGroupScheme entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public List<PumpGroupScheme> saveAll(List<PumpGroupSchemeSaveRequest> entities) {
        return QueryUtil.saveOrUpdateBatchByRequest(entities, mapper::saveAll, mapper::updateAll);
    }

    @Override
    public List<PumpGroupScheme> findByPumpRoomId(String pumpRoomId) {
        return mapper.findByPumpRoomId(pumpRoomId);
    }

    @Override
    public PumpGroupScheme findBySchemeCode(String schemeCode) {
        return mapper.findBySchemeCode(schemeCode);
    }

}
