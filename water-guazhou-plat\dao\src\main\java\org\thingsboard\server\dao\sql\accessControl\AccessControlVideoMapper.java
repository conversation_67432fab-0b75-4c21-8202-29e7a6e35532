package org.thingsboard.server.dao.sql.accessControl;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.AccessControlVideo;
import org.thingsboard.server.dao.model.sql.VideoEntity;

import java.util.List;

/**
 * 门禁视频关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2024
 */
@Mapper
public interface AccessControlVideoMapper extends BaseMapper<AccessControlVideo> {
    
    /**
     * 根据门禁ID查询关联的视频列表
     * 
     * @param accessControlId 门禁ID
     * @return 视频列表
     */
    List<VideoEntity> findVideosByAccessControlId(@Param("accessControlId") String accessControlId);
    
    /**
     * 根据视频ID查询关联信息
     * 
     * @param videoId 视频ID
     * @param tenantId 租户ID
     * @return 关联信息列表
     */
    List<AccessControlVideo> findByVideoId(@Param("videoId") String videoId, @Param("tenantId") String tenantId);
    
    /**
     * 根据门禁ID查询关联信息
     * 
     * @param accessControlId 门禁ID
     * @param tenantId 租户ID
     * @return 关联信息列表
     */
    List<AccessControlVideo> findByAccessControlId(@Param("accessControlId") String accessControlId, @Param("tenantId") String tenantId);
    
    /**
     * 根据门禁ID删除关联
     * 
     * @param accessControlId 门禁ID
     * @return 删除的记录数
     */
    int deleteByAccessControlId(@Param("accessControlId") String accessControlId);
    
    /**
     * 根据视频ID删除关联
     * 
     * @param videoId 视频ID
     * @return 删除的记录数
     */
    int deleteByVideoId(@Param("videoId") String videoId);
    
    /**
     * 根据门禁ID和视频ID查询关联
     * 
     * @param accessControlId 门禁ID
     * @param videoId 视频ID
     * @return 关联信息
     */
    AccessControlVideo findByAccessControlIdAndVideoId(@Param("accessControlId") String accessControlId, @Param("videoId") String videoId);
    
    /**
     * 根据租户ID查询所有关联
     * 
     * @param tenantId 租户ID
     * @return 关联信息列表
     */
    List<AccessControlVideo> findByTenantId(@Param("tenantId") String tenantId);
    
    /**
     * 统计门禁关联的视频数量
     * 
     * @param accessControlId 门禁ID
     * @return 视频数量
     */
    int countVideosByAccessControlId(@Param("accessControlId") String accessControlId);
    
    /**
     * 统计视频关联的门禁数量
     * 
     * @param videoId 视频ID
     * @return 门禁数量
     */
    int countAccessControlsByVideoId(@Param("videoId") String videoId);
}
