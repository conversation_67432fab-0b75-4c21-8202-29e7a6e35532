package org.thingsboard.server.dao.model.sql.deviceIcon;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseTenantName;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;

import java.util.Date;

/**
 * 设备图标实体
 */
@Data
@TableName("tb_device_icon")
public class DeviceIcon {
    /**
     * ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    
    /**
     * 设备类型
     */
    private String deviceType;
    
    /**
     * 图标URL
     */
    private String iconUrl;
    
    /**
     * 设备状态
     */
    private String deviceStatus;

    /**
     * 状态颜色
     */
    private String statusColor;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 租户ID
     */
    private String tenantId;
}
