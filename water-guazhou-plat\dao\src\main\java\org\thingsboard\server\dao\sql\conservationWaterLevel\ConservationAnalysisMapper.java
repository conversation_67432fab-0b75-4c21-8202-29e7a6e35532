package org.thingsboard.server.dao.sql.conservationWaterLevel;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.conservationWaterLevel.ConservationAnalysis;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 涵养水位分析结果Mapper
 */
@Mapper
public interface ConservationAnalysisMapper extends BaseMapper<ConservationAnalysis> {

    /**
     * 分页查询分析结果
     */
    List<ConservationAnalysis> getList(@Param("params") Map<String, Object> params);

    /**
     * 查询分析结果总数
     */
    int getCount(@Param("params") Map<String, Object> params);

    /**
     * 获取测点最新的分析结果
     */
    ConservationAnalysis getLatestAnalysis(@Param("stationId") String stationId);

    /**
     * 根据时间范围查询分析结果
     */
    List<ConservationAnalysis> getAnalysisByTimeRange(
            @Param("stationId") String stationId,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime
    );

    /**
     * 获取风险等级统计
     */
    List<Map<String, Object>> getRiskLevelStatistics(@Param("tenantId") String tenantId);

    /**
     * 获取涵养潜力趋势数据
     */
    List<Map<String, Object>> getConservationPotentialTrend(
            @Param("stationId") String stationId,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime
    );
}
