package org.thingsboard.server.controller.deviceIcon;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.deviceIcon.DeviceIconService;
import org.thingsboard.server.dao.model.sql.deviceIcon.DeviceIcon;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.dao.util.imodel.query.deviceIcon.DeviceIconBatchSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.deviceIcon.DeviceIconPageRequest;
import org.thingsboard.server.dao.util.imodel.query.deviceIcon.DeviceIconSaveRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 设备图标控制器
 */
@Api(tags = "设备图标管理")
@RestController
@RequestMapping("/api/deviceIcon")
public class DeviceIconController extends BaseController {

    @Autowired
    private DeviceIconService deviceIconService;

    @ApiOperation(value = "分页查询设备图标")
    @GetMapping
    public IstarResponse findByPage(DeviceIconPageRequest request) throws ThingsboardException {
        request.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        return IstarResponse.ok(deviceIconService.findByPage(request));
    }

    @ApiOperation(value = "根据ID查询设备图标")
    @GetMapping("/{id}")
    public IstarResponse findById(@PathVariable String id) {
        return IstarResponse.ok(deviceIconService.findById(id));
    }

    @ApiOperation(value = "根据设备类型查询设备图标")
    @GetMapping("/deviceType/{deviceType}")
    public IstarResponse findByDeviceType(@PathVariable String deviceType) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(deviceIconService.findByDeviceType(deviceType, tenantId));
    }

    @ApiOperation(value = "根据设备类型和设备状态查询设备图标")
    @GetMapping("/deviceType/{deviceType}/status/{deviceStatus}")
    public IstarResponse findByDeviceTypeAndStatus(@PathVariable String deviceType, @PathVariable String deviceStatus) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        DeviceIcon deviceIcon = deviceIconService.findByDeviceTypeAndStatus(deviceType, deviceStatus, tenantId);
        return IstarResponse.ok(deviceIcon);
    }

    @ApiOperation(value = "保存设备图标")
    @PostMapping
    public IstarResponse save(@RequestBody DeviceIconSaveRequest request) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(deviceIconService.save(request,tenantId));
    }

    @ApiOperation(value = "批量保存设备图标")
    @PostMapping("/batch")
    public IstarResponse batchSave(@RequestBody DeviceIconBatchSaveRequest request) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        deviceIconService.batchSave(request, tenantId);
        return IstarResponse.ok();
    }

    @ApiOperation(value = "删除设备图标")
    @DeleteMapping("/{id}")
    public IstarResponse delete(@PathVariable String id) {
        return IstarResponse.ok(deviceIconService.delete(id));
    }

    @ApiOperation(value = "根据设备类型删除设备图标")
    @DeleteMapping("/deviceType/{deviceType}")
    public IstarResponse deleteByDeviceType(@PathVariable String deviceType) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        deviceIconService.deleteByDeviceType(deviceType, tenantId);
        return IstarResponse.ok();
    }
}
