package org.thingsboard.server.dao.util.imodel.query.waterPlant;

import lombok.Data;
import org.thingsboard.server.dao.model.sql.waterPlant.WaterPlantPressure;
import org.thingsboard.server.dao.util.imodel.query.PageableQueryEntity;

import java.util.Date;

/**
 * 水厂压力信息分页查询请求
 */
@Data
public class WaterPlantPressurePageRequest extends PageableQueryEntity<WaterPlantPressure> {
    /**
     * 水厂ID
     */
    private String waterPlantId;

    /**
     * 水厂名称
     */
    private String waterPlantName;

    /**
     * 设备编号
     */
    private String deviceSerial;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 租户ID
     */
    private String tenantId;
}
