import { request } from '@/plugins/axios'

// 获取模型文件列表
export function getTemplateFileList(params: any) {
  return request({
    url: '/api/base/template/file/list',
    method: 'get',
    params
  })
}

// 获取模型文件详情
export function getTemplateFileDetail(id: string) {
  return request({
    url: '/api/base/template/file/getDetail',
    method: 'get',
    params: { id }
  })
}

// 新增模型文件配置
export function addTemplateFile(data: any) {
  return request({
    url: '/api/base/template/file/add',
    method: 'post',
    data
  })
}

// 修改模型文件配置
export function editTemplateFile(data: any) {
  return request({
    url: '/api/base/template/file/edit',
    method: 'post',
    data
  })
}

// 删除模型文件配置
export function deleteTemplateFile(ids: string[]) {
  return request({
    url: '/api/base/template/file/deleteIds',
    method: 'delete',
    data: ids
  })
} 