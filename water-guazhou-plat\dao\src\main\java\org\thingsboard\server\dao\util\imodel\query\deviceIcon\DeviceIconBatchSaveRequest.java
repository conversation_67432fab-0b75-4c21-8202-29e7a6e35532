package org.thingsboard.server.dao.util.imodel.query.deviceIcon;

import lombok.Data;

import java.util.List;

/**
 * 设备图标批量保存请求
 */
@Data
public class DeviceIconBatchSaveRequest {
    /**
     * 设备类型
     */
    private String deviceType;
    
    /**
     * 图标列表
     */
    private List<IconItem> iconList;
    
    /**
     * 图标项
     */
    @Data
    public static class IconItem {
        /**
         * 图标URL
         */
        private String iconUrl;
        
        /**
         * 设备状态
         */
        private String deviceStatus;
        
        /**
         * 状态颜色
         */
        private String statusColor;
    }
}
