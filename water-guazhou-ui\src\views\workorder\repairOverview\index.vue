<!-- 维修总览 -->
<template>
  <div class="wrapper">
    <!-- 查询条件 -->
    <SLCard class="card" title=" " overlay>
      <template #title>
        <Search ref="refSearch" :config="SearchConfig"></Search>
      </template>
      
      <!-- 图表区域 -->
      <div class="chart-container">
        <!-- 左侧统计图表 -->
        <div class="chart-left">
          <div class="chart-header">
            <span class="chart-title">维修统计</span>
            <div class="chart-legend">
              <div class="legend-item">
                <span class="legend-color" style="background-color: #5B9BD5;"></span>
                <span class="legend-text">水表问题</span>
                <span class="legend-value">{{ state.summary.waterSupplyTotal }}</span>
              </div>
              <div class="legend-item">
                <span class="legend-color" style="background-color: #70AD47;"></span>
                <span class="legend-text">热表设备</span>
                <span class="legend-value">{{ state.summary.heatSupplyTotal }}</span>
              </div>
              <div class="legend-item">
                <span class="legend-color" style="background-color: #FFC000;"></span>
                <span class="legend-text">管网维修</span>
                <span class="legend-value">{{ state.summary.pipelineTotal }}</span>
              </div>
              <div class="legend-item">
                <span class="legend-color" style="background-color: #264478;"></span>
                <span class="legend-text">阀门维修</span>
                <span class="legend-value">{{ state.summary.valveTotal }}</span>
              </div>
            </div>
          </div>
          <VChart ref="refBarChart" :option="state.barOption" style="height: 300px;"></VChart>
        </div>
        
        <!-- 右侧饼图 -->
        <div class="chart-right">
          <VChart ref="refPieChart" :option="state.pieOption" style="height: 300px;"></VChart>
        </div>
      </div>
    </SLCard>

    <!-- 维修记录表格 -->
    <SLCard class="card table-card" title="维修记录" overlay>
      <FormTable :config="TableConfig"></FormTable>
    </SLCard>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { Search as SearchIcon } from '@element-plus/icons-vue'
import { ISearchIns } from '@/components/type'
import { IECharts } from '@/plugins/echart'
import { RepairOverviewBarOption, RepairTypePieOption } from './echart'
import { GetRepairOverviewStatistics, GetRepairRecordPage, type RepairOverviewStatistics } from '@/api/workorder/repairOverview'
import { formatterDate } from '@/utils/GlobalHelper'
import moment from 'moment'

const refSearch = ref<ISearchIns>()
const refBarChart = ref<IECharts>()
const refPieChart = ref<IECharts>()

// 搜索配置
const SearchConfig = reactive<ISearch>({
  filters: [
    {
      type: 'radio-button',
      field: 'type',
      label: '统计类型',
      value: 'year',
      options: [
        { label: '年度', value: 'year' },
        { label: '月度', value: 'month' }
      ]
    },
    {
      type: 'year',
      field: 'year',
      label: '年份',
      value: moment().format('YYYY'),
      show: (form: any) => form.type === 'year'
    },
    {
      type: 'month',
      field: 'month',
      label: '月份',
      value: moment().format('YYYY-MM'),
      show: (form: any) => form.type === 'month'
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          text: '查询',
          type: 'primary',
          click: () => refreshData()
        },
        {
          text: '重置',
          type: 'default',
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        }
      ]
    }
  ]
})

// 状态管理
const state = reactive<{
  barOption: any
  pieOption: any
  summary: {
    waterSupplyTotal: number
    heatSupplyTotal: number
    pipelineTotal: number
    valveTotal: number
  }
}>({
  barOption: null,
  pieOption: null,
  summary: {
    waterSupplyTotal: 0,
    heatSupplyTotal: 0,
    pipelineTotal: 0,
    valveTotal: 0
  }
})

// 表格配置
const TableConfig = reactive<ITable>({
  columns: [
    { label: '维修部件', field: 'deviceName', width: 120 },
    { label: '上报地址', field: 'location', width: 200 },
    { label: '维修类型', field: 'repairType', width: 100 },
    { label: '维修状态', field: 'repairStatus', width: 100 },
    { 
      label: '创建时间', 
      field: 'createTime', 
      width: 160,
      formatter: (row: any) => formatterDate(row.createTime)
    },
    { label: '创建人', field: 'creator', width: 100 },
    { 
      label: '耗时', 
      field: 'duration', 
      width: 80,
      formatter: (row: any) => `${row.duration}小时`
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
    change: (page: number, size: number) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshTableData()
    }
  }
})

// 刷新图表数据
const refreshData = async () => {
  try {
    const query = refSearch.value?.queryParams || {}
    const params = {
      year: query.type === 'year' ? query.year : undefined,
      startTime: query.type === 'month' ? moment(query.month).startOf('month').format('YYYY-MM-DD') : undefined,
      endTime: query.type === 'month' ? moment(query.month).endOf('month').format('YYYY-MM-DD') : undefined
    }

    const res = await GetRepairOverviewStatistics(params)
    const data: RepairOverviewStatistics = res.data?.data || {}
    
    // 更新图表
    state.barOption = RepairOverviewBarOption(data.monthlyData || [])
    state.pieOption = RepairTypePieOption(data.summary || state.summary)
    state.summary = data.summary || state.summary
    
    // 更新表格数据
    TableConfig.dataList = data.repairRecords || []
    TableConfig.pagination.total = data.repairRecords?.length || 0
  } catch (error) {
    // 使用模拟数据
    loadMockData()
  }
}

// 刷新表格数据
const refreshTableData = async () => {
  try {
    const query = refSearch.value?.queryParams || {}
    const params = {
      page: TableConfig.pagination.page,
      size: TableConfig.pagination.limit,
      year: query.type === 'year' ? query.year : undefined,
      startTime: query.type === 'month' ? moment(query.month).startOf('month').format('YYYY-MM-DD') : undefined,
      endTime: query.type === 'month' ? moment(query.month).endOf('month').format('YYYY-MM-DD') : undefined
    }

    const res = await GetRepairRecordPage(params)
    const data = res.data?.data || {}
    
    TableConfig.dataList = data.data || []
    TableConfig.pagination.total = data.total || 0
  } catch (error) {
    // 使用模拟数据
    loadMockTableData()
  }
}

// 加载模拟数据
const loadMockData = () => {
  const mockMonthlyData = [
    { month: '1月', waterSupplyIssues: 73, heatSupplyIssues: 20, pipelineMaintenance: 56, valveMaintenance: 43 },
    { month: '2月', waterSupplyIssues: 45, heatSupplyIssues: 15, pipelineMaintenance: 32, valveMaintenance: 28 },
    { month: '3月', waterSupplyIssues: 62, heatSupplyIssues: 18, pipelineMaintenance: 41, valveMaintenance: 35 },
    { month: '4月', waterSupplyIssues: 58, heatSupplyIssues: 22, pipelineMaintenance: 38, valveMaintenance: 31 },
    { month: '5月', waterSupplyIssues: 71, heatSupplyIssues: 25, pipelineMaintenance: 47, valveMaintenance: 39 },
    { month: '6月', waterSupplyIssues: 89, heatSupplyIssues: 28, pipelineMaintenance: 53, valveMaintenance: 45 },
    { month: '7月', waterSupplyIssues: 95, heatSupplyIssues: 31, pipelineMaintenance: 58, valveMaintenance: 48 },
    { month: '8月', waterSupplyIssues: 78, heatSupplyIssues: 26, pipelineMaintenance: 44, valveMaintenance: 37 },
    { month: '9月', waterSupplyIssues: 84, heatSupplyIssues: 29, pipelineMaintenance: 49, valveMaintenance: 41 },
    { month: '10月', waterSupplyIssues: 67, heatSupplyIssues: 23, pipelineMaintenance: 36, valveMaintenance: 33 },
    { month: '11月', waterSupplyIssues: 52, heatSupplyIssues: 19, pipelineMaintenance: 29, valveMaintenance: 26 },
    { month: '12月', waterSupplyIssues: 61, heatSupplyIssues: 21, pipelineMaintenance: 34, valveMaintenance: 30 }
  ]

  const mockSummary = {
    waterSupplyTotal: 735,
    heatSupplyTotal: 277,
    pipelineTotal: 517,
    valveTotal: 436
  }

  state.barOption = RepairOverviewBarOption(mockMonthlyData)
  state.pieOption = RepairTypePieOption(mockSummary)
  state.summary = mockSummary
}

// 加载模拟表格数据
const loadMockTableData = () => {
  const mockTableData = [
    {
      id: '1',
      deviceName: 'A1控制阀组',
      location: '瓜州县中心港路',
      repairType: '金属维修',
      repairStatus: '待办',
      createTime: '2024-12-19 14:39:57',
      creator: 'admin',
      duration: 45
    },
    {
      id: '2', 
      deviceName: '室外工厂大楼',
      location: '水表问题/港路',
      repairType: '管道维修',
      repairStatus: '已办',
      createTime: '2024-12-19 14:39:57',
      creator: 'admin',
      duration: 50
    },
    {
      id: '3',
      deviceName: 'A1阀门设备',
      location: '阀门维修/港路',
      repairType: '金属维修',
      repairStatus: '已办',
      createTime: '2024-12-19 14:39:57',
      creator: 'admin',
      duration: 49
    },
    {
      id: '4',
      deviceName: '向城区供水管道',
      location: '管网维修/港路',
      repairType: '管道维修',
      repairStatus: '已办',
      createTime: '2024-12-19 14:39:57',
      creator: 'admin',
      duration: 50
    }
  ]

  TableConfig.dataList = mockTableData
  TableConfig.pagination.total = mockTableData.length
}

onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.wrapper {
  padding: 20px;
  background-color: #f5f5f5;
}

.card {
  margin-bottom: 20px;
}

.chart-container {
  display: flex;
  gap: 20px;
  margin-top: 20px;
}

.chart-left {
  flex: 2;
}

.chart-right {
  flex: 1;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.chart-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.chart-legend {
  display: flex;
  gap: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-text {
  font-size: 12px;
  color: #666;
}

.legend-value {
  font-size: 12px;
  font-weight: bold;
  color: #333;
}

.table-card {
  :deep(.sl-card__body) {
    padding: 0;
  }
}
</style>
