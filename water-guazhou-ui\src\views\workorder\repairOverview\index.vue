<!-- 维修总览 -->
<template>
  <div class="wrapper">
    <!-- 查询条件 -->
    <SLCard class="card" title=" " overlay>
      <template #title>
        <Search ref="refSearch" :config="SearchConfig"></Search>
      </template>
      
      <!-- 图表区域 -->
      <div class="chart-container">
        <!-- 左侧统计图表 -->
        <div class="chart-left">
          <div class="chart-header">
            <span class="chart-title">维修统计</span>
            <div class="chart-legend">
              <div class="legend-item">
                <span class="legend-color" style="background-color: #5B9BD5;"></span>
                <span class="legend-text">水表问题</span>
                <span class="legend-value">{{ state.summary.waterSupplyTotal }}</span>
              </div>
              <div class="legend-item">
                <span class="legend-color" style="background-color: #70AD47;"></span>
                <span class="legend-text">热表设备</span>
                <span class="legend-value">{{ state.summary.heatSupplyTotal }}</span>
              </div>
              <div class="legend-item">
                <span class="legend-color" style="background-color: #FFC000;"></span>
                <span class="legend-text">管网维修</span>
                <span class="legend-value">{{ state.summary.pipelineTotal }}</span>
              </div>
              <div class="legend-item">
                <span class="legend-color" style="background-color: #264478;"></span>
                <span class="legend-text">阀门维修</span>
                <span class="legend-value">{{ state.summary.valveTotal }}</span>
              </div>
            </div>
          </div>
          <VChart ref="refBarChart" :option="state.barOption" style="height: 300px;"></VChart>
        </div>
        
        <!-- 右侧饼图 -->
        <div class="chart-right">
          <VChart ref="refPieChart" :option="state.pieOption" style="height: 300px;"></VChart>
        </div>
      </div>
    </SLCard>

    <!-- 维修记录表格 -->
    <SLCard class="card table-card" title="维修记录" overlay>
      <FormTable :config="TableConfig"></FormTable>
    </SLCard>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { Search as SearchIcon } from '@element-plus/icons-vue'
import { ISearchIns } from '@/components/type'
import { IECharts } from '@/plugins/echart'
import { RepairOverviewBarOption, RepairTypePieOption } from './echart'
import { GetRepairOverviewStatistics, GetRepairOverviewPage, type RepairOverviewStatistics } from '@/api/workorder/repairOverview'
import moment from 'moment'

const refSearch = ref<ISearchIns>()
const refBarChart = ref<IECharts>()
const refPieChart = ref<IECharts>()

// 查询按钮处理函数
const handleQuery = () => {
  refreshData()
}

// 重置按钮处理函数
const handleReset = () => {
  refSearch.value?.resetForm()
  refreshData()
}

// 导出数据处理函数
const handleExport = () => {
  console.log('导出维修总览数据')
  // TODO: 实现数据导出功能
}

// 搜索配置
const SearchConfig = {
  filters: [
    {
      type: 'datetime-range',
      field: 'createTimeRange',
      label: '创建时间',
      placeholder: ['2024-12-19 14:39:57', '2025-06-19 14:39:57'],
      defaultValue: [
        moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss'),
        moment().format('YYYY-MM-DD HH:mm:ss')
      ],
      format: 'YYYY-MM-DD HH:mm:ss'
    } as any,
    {
      type: 'datetime-range',
      field: 'expireTimeRange',
      label: '到期时间',
      placeholder: ['开始时间', '结束时间'],
      format: 'YYYY-MM-DD HH:mm:ss'
    } as any,
    {
      type: 'select',
      field: 'status',
      label: '状态查询',
      placeholder: '请选择',
      options: [
        { label: '待办', value: 'pending' },
        { label: '已办', value: 'completed' },
        { label: '处理中', value: 'processing' }
      ]
    } as any,
    {
      type: 'btn-group',
      btns: [
        {
          text: '查询',
          type: 'primary',
          click: handleQuery
        },
        {
          text: '重置',
          type: 'default',
          click: handleReset
        },
        {
          text: '导出数据',
          type: 'warning',
          click: handleExport
        }
      ]
    } as any
  ]
}

// 状态管理
const state = reactive<{
  barOption: any
  pieOption: any
  summary: {
    waterSupplyTotal: number
    heatSupplyTotal: number
    pipelineTotal: number
    valveTotal: number
  }
}>({
  barOption: null,
  pieOption: null,
  summary: {
    waterSupplyTotal: 0,
    heatSupplyTotal: 0,
    pipelineTotal: 0,
    valveTotal: 0
  }
})

// 刷新表格数据
const refreshTableData = async () => {
  try {
    const query = refSearch.value?.queryParams || {}
    const params = {
      page: TableConfig.pagination.page,
      size: TableConfig.pagination.limit,
      createFromTime: query.createTimeRange?.[0] ? moment(query.createTimeRange[0]).valueOf() : moment().subtract(7, 'days').valueOf(),
      createToTime: query.createTimeRange?.[1] ? moment(query.createTimeRange[1]).valueOf() : moment().valueOf(),
      expireFromTime: query.expireTimeRange?.[0] ? moment(query.expireTimeRange[0]).valueOf() : undefined,
      expireToTime: query.expireTimeRange?.[1] ? moment(query.expireTimeRange[1]).valueOf() : undefined,
      status: query.status
    }

    const res = await GetRepairOverviewPage(params as any)
    const data = res.data || {}

    // 新的API返回标准分页格式
    TableConfig.dataList = data.records || []
    TableConfig.pagination.total = data.total || 0
  } catch (error) {
    console.error('获取维修记录数据失败:', error)
    // 设置空数据
    TableConfig.dataList = []
    TableConfig.pagination.total = 0
  }
}

// 分页变更处理函数
const handlePageChange = (page: number, size: number) => {
  TableConfig.pagination.page = page
  TableConfig.pagination.limit = size
  refreshTableData()
}

// 表格配置
const TableConfig = reactive({
  columns: [
    { label: '维修事件', prop: 'deviceName', minWidth: 150 },
    { label: '上报地址', prop: 'location', minWidth: 200 },
    { label: '维修类型', prop: 'repairType', minWidth: 120 },
    { label: '维修状态', prop: 'repairStatus', minWidth: 100 },
    {
      label: '创建时间',
      prop: 'createTime',
      minWidth: 180,
      formatter: (row: any) => moment(row.createTime).format('YYYY-MM-DD HH:mm:ss')
    },
    { label: '创建人', prop: 'creator', minWidth: 100 },
    {
      label: '耗时',
      prop: 'duration',
      minWidth: 100,
      formatter: (row: any) => `${row.duration}小时`
    }
  ],
  dataList: [] as any[],
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
    change: handlePageChange
  }
})

// 刷新图表数据
const refreshData = async () => {
  try {
    const query = refSearch.value?.queryParams || {}

    // 处理创建时间范围，转换为时间戳
    const [createFromTime, createToTime] = query.createTimeRange?.length === 2
      ? [
          moment(query.createTimeRange[0]).valueOf(),
          moment(query.createTimeRange[1]).valueOf()
        ]
      : [
          moment().subtract(7, 'days').valueOf(),
          moment().valueOf()
        ]

    // 处理到期时间范围，转换为时间戳
    const [expireFromTime, expireToTime] = query.expireTimeRange?.length === 2
      ? [
          moment(query.expireTimeRange[0]).valueOf(),
          moment(query.expireTimeRange[1]).valueOf()
        ]
      : [null, null]

    const params = {
      createFromTime,
      createToTime,
      expireFromTime,
      expireToTime,
      status: query.status
    }

    const res = await GetRepairOverviewStatistics(params as any)
    const data: RepairOverviewStatistics = res.data?.data || {}
    
    // 更新图表
    state.barOption = RepairOverviewBarOption(data.monthlyData || [])
    state.pieOption = RepairTypePieOption(data.summary || state.summary)
    state.summary = data.summary || state.summary
    
    // 更新表格数据
    TableConfig.dataList = data.repairRecords || []
    TableConfig.pagination.total = data.repairRecords?.length || 0
  } catch (error) {
    console.error('获取维修总览数据失败:', error)
    // 设置空数据
    state.barOption = RepairOverviewBarOption([])
    state.pieOption = RepairTypePieOption({ waterSupplyTotal: 0, heatSupplyTotal: 0, pipelineTotal: 0, valveTotal: 0 })
    state.summary = { waterSupplyTotal: 0, heatSupplyTotal: 0, pipelineTotal: 0, valveTotal: 0 }
    TableConfig.dataList = []
    TableConfig.pagination.total = 0
  }
}

onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.wrapper {
  padding: 20px;
  background-color: #f5f5f5;
}

.card {
  margin-bottom: 20px;
}

.chart-container {
  display: flex;
  gap: 20px;
  margin-top: 20px;
}

.chart-left {
  flex: 2;
}

.chart-right {
  flex: 1;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.chart-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.chart-legend {
  display: flex;
  gap: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-text {
  font-size: 12px;
  color: #666;
}

.legend-value {
  font-size: 12px;
  font-weight: bold;
  color: #333;
}

.table-card {
  :deep(.sl-card__body) {
    padding: 20px;
  }

  :deep(.el-table) {
    width: 100% !important;
  }

  :deep(.el-table__body-wrapper) {
    width: 100% !important;
  }

  :deep(.el-table th) {
    text-align: center;
  }

  :deep(.el-table td) {
    text-align: center;
  }
}
</style>
