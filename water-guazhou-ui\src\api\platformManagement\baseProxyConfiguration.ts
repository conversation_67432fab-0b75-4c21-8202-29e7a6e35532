import { request } from '@/plugins/axios'

// 获取代理配置列表
export function getProxyConfigList(params: any) {
  return request({
    url: '/api/base/proxy/configuration/list',
    method: 'get',
    params
  })
}

// 获取代理配置详情
export function getProxyConfigDetail(id: string) {
  return request({
    url: '/api/base/proxy/configuration/getDetail',
    method: 'get',
    params: { id }
  })
}

// 新增代理配置配置
export function addProxyConfig(data: any) {
  return request({
    url: '/api/base/proxy/configuration/add',
    method: 'post',
    data
  })
}

// 修改代理配置配置
export function editProxyConfig(data: any) {
  return request({
    url: '/api/base/proxy/configuration/edit',
    method: 'post',
    data
  })
}

// 删除代理配置配置
export function deleteProxyConfig(ids: string[]) {
  return request({
    url: '/api/base/proxy/configuration/deleteIds',
    method: 'delete',
    data: ids
  })
} 