<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      class="card-table"
      :config="TableConfig"
    ></CardTable>
    <DialogForm
      ref="refDialogForm"
      :config="DialogFormConfig"
    ></DialogForm>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue'
import { 
  getProxyConfigList,
  addProxyConfig,
  editProxyConfig,
  deleteProxyConfig,
  getProxyConfigDetail
} from '@/api/platformManagement/baseProxyConfiguration'
import { SLConfirm, SLMessage } from '@/utils/Message'

const refSearch = ref()
const refDialogForm = ref()

const SearchConfig = reactive({
  labelWidth: '100px',
  filters: [
    { 
      type: 'input', 
      label: '配置名称', 
      field: 'configName', 
      placeholder: '请输入配置名称',
      onChange: () => refreshData() 
    },
    {
      type: 'btn-group',
      btns: [
        { type: 'primary', perm: true, text: '查询', click: () => refreshData() },
        { perm: true, type: 'primary', text: '新增', click: () => handleAdd() },
        { perm: true, type: 'danger', text: '批量删除', click: () => handleDelete() }
      ]
    }
  ],
  defaultParams: {}
})

const TableConfig = reactive({
  columns: [
    { label: '配置文件信息', prop: 'proxyConfig' },
    { label: '版本信息', prop: 'version' },
    { label: '状态', prop: 'status' },
    { label: '处理进程数', prop: 'workerProcesses' },
    { label: '最大并发链接数', prop: 'workerConnections' },
    { label: '连接超时时间', prop: 'keepAliveTimeout' },
    { label: '日志状态', prop: 'logStatus' },
    { label: '缓存状态', prop: 'cacheStatus' }
  ],
  dataList: [],
  operations: [
    {
      perm: true,
      type: 'primary',
      isTextBtn: true,
      text: '查看详情',
      click: (row) => handleDetail(row)
    },
    {
      perm: true,
      type: 'primary',
      isTextBtn: true,
      text: '编辑',
      click: (row) => handleAdd(row)
    },
    {
      perm: true,
      type: 'danger',
      isTextBtn: true,
      text: '删除',
      click: (row) => handleDelete(row)
    }
  ],
  pagination: {
    total: 0,
    page: 1,
    align: 'right',
    limit: 20,
    handlePage: (page) => {
      TableConfig.pagination.page = page
      refreshData()
    },
    handleSize: (size) => {
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  handleSelectChange: (rows) => {
    TableConfig.selectList = rows || []
  }
})

const DialogFormConfig = reactive({
  title: '新增代理配置',
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '配置文件信息',
          field: 'proxyConfig',
          rules: [{ required: true, message: '请输入配置文件信息' }]
        },
        {
          type: 'input',
          label: '版本信息',
          field: 'version',
          rules: [{ required: true, message: '请输入版本信息' }]
        },
        {
          type: 'input',
          label: '状态',
          field: 'status',
          rules: [{ required: true, message: '请输入状态' }]
        },
        {
          type: 'input',
          label: '处理进程数',
          field: 'workerProcesses',
          rules: [{ required: true, message: '请输入处理进程数' }]
        },
        {
          type: 'input',
          label: '最大并发链接数',
          field: 'workerConnections',
          rules: [{ required: true, message: '请输入最大并发链接数' }]
        },
        {
          type: 'input',
          label: '连接超时时间',
          field: 'keepAliveTimeout',
          rules: [{ required: true, message: '请输入连接超时时间' }]
        },
        {
          type: 'input',
          label: '日志状态',
          field: 'logStatus',
          rules: [{ required: true, message: '请输入日志状态' }]
        },
        {
          type: 'input',
          label: '缓存状态',
          field: 'cacheStatus',
          rules: [{ required: true, message: '请输入缓存状态' }]
        }
      ]
    }
  ],
  labelPosition: 'top',
  defaultValue: {},
  dialogWidth: 600,
  draggable: true,
  showSubmit: true,
  showCancel: true,
  cancelText: '取消',
  submitText: '确定',
  submit: async (params) => {
    try {
      if (params.id) {
        await editProxyConfig(params)
        SLMessage.success('修改成功')
      } else {
        await addProxyConfig(params)
        SLMessage.success('新增成功')
      }
      refDialogForm.value?.closeDialog()
      refreshData()
    } catch (error) {
      SLMessage.error('操作失败')
    }
  }
})

// 重置对话框配置
const resetDialogConfig = () => {
  DialogFormConfig.group[0].fields.forEach(field => {
    field.disabled = false
    field.readonly = false
  })
  
  DialogFormConfig.showSubmit = true
  DialogFormConfig.showCancel = true
  DialogFormConfig.cancelText = '取消'
  DialogFormConfig.submitText = '确定'
  
  DialogFormConfig.submit = async (params) => {
    try {
      if (params.id) {
        await editProxyConfig(params)
        SLMessage.success('修改成功')
      } else {
        await addProxyConfig(params)
        SLMessage.success('新增成功')
      }
      refDialogForm.value?.closeDialog()
      refreshData()
    } catch (error) {
      SLMessage.error('操作失败')
    }
  }
}

// 查看详情
const handleDetail = async (row) => {
  try {
    const res = await getProxyConfigDetail(row.id)
    const detailData = res.data?.data || res
    
    resetDialogConfig()
    DialogFormConfig.title = '代理配置详情'
    DialogFormConfig.defaultValue = { ...detailData }
    DialogFormConfig.group[0].fields.forEach(field => {
      field.disabled = true
    })
    DialogFormConfig.showSubmit = false
    DialogFormConfig.cancelText = '关闭'
    refDialogForm.value?.openDialog()
  } catch (error) {
    SLMessage.error('获取详情失败')
  }
}

// 新增/编辑
const handleAdd = (row) => {
  resetDialogConfig()
  
  if (row) {
    DialogFormConfig.title = '编辑代理配置'
    DialogFormConfig.defaultValue = { ...row }
  } else {
    DialogFormConfig.title = '新增代理配置'
    DialogFormConfig.defaultValue = {}
  }
  
  refDialogForm.value?.openDialog()
}

// 删除
const handleDelete = async (row) => {
  try {
    const ids = row ? [row.id] : TableConfig.selectList.map(item => item.id)
    if (!ids.length) {
      SLMessage.warning('请选择要删除的数据')
      return
    }
    
    await SLConfirm('确定要删除选中的数据吗？')
    await deleteProxyConfig(ids)
    SLMessage.success('删除成功')
    refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      SLMessage.error('删除失败')
    }
  }
}

// 刷新数据
const refreshData = async () => {
  try {
    const res = await getProxyConfigList({
      page: TableConfig.pagination.page,
      size: TableConfig.pagination.limit,
      ...(refSearch.value?.queryParams || {})
    })
    const responseData = res.data?.data || res
    TableConfig.dataList = responseData.records || responseData
    TableConfig.pagination.total = responseData.total || responseData.length || 0
  } catch (error) {
    SLMessage.error('数据加载失败')
  }
}

onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.wrapper {
  padding: 20px;
}

.card-table {
  margin-top: 20px;
}
</style>