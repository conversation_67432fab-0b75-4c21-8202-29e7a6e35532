package org.thingsboard.server.dao.orderWork;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.thingsboard.server.dao.mapper.workOrder.EventOverviewMapper;
import org.thingsboard.server.dao.model.sql.workOrder.EventOverview;
import org.thingsboard.server.dao.model.sql.workOrder.EventStatusEnums;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStatus;
import org.thingsboard.server.dao.util.imodel.query.workOrder.EventOverviewPageRequest;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 事件总览服务实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class EventOverviewServiceImpl implements EventOverviewService {

    @Autowired
    private EventOverviewMapper eventOverviewMapper;

    @Override
    public IPage<EventOverview> findEventOverviewByPage(EventOverviewPageRequest request) {
        Page<EventOverview> page = new Page<>(request.getPage(), request.getSize());
        QueryWrapper<EventOverview> queryWrapper = buildQueryWrapper(request);
        
        // 设置排序
        if (StringUtils.hasText(request.getOrderBy())) {
            String orderBy = convertOrderByField(request.getOrderBy());
            if ("DESC".equalsIgnoreCase(request.getOrderDirection())) {
                queryWrapper.orderByDesc(orderBy);
            } else {
                queryWrapper.orderByAsc(orderBy);
            }
        } else {
            queryWrapper.orderByDesc("create_time");
        }

        return eventOverviewMapper.selectPage(page, queryWrapper);
    }

    @Override
    public EventOverview findEventOverviewById(String id, String tenantId) {
        QueryWrapper<EventOverview> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        if (StringUtils.hasText(tenantId)) {
            queryWrapper.eq("tenant_id", tenantId);
        }

        return eventOverviewMapper.selectOne(queryWrapper);
    }

    @Override
    public EventOverview createEventOverview(Map<String, Object> eventData, String tenantId) {
        EventOverview eventOverview = new EventOverview();

        // 生成ID
        eventOverview.setId(UUID.randomUUID().toString().replace("-", ""));

        // 设置基本信息
        eventOverview.setTitle((String) eventData.get("title"));
        eventOverview.setType((String) eventData.get("type"));
        eventOverview.setTypeName((String) eventData.get("typeName"));
        eventOverview.setAddress((String) eventData.get("address"));
        eventOverview.setRemark((String) eventData.get("remark"));
        eventOverview.setCoordinate((String) eventData.get("coordinate"));
        eventOverview.setCoordinateName((String) eventData.get("coordinateName"));

        // 设置状态为待审核
        eventOverview.setStatus(EventStatusEnums.PENDING_REVIEW);

        // 设置租户和时间信息
        eventOverview.setTenantId(tenantId);
        eventOverview.setCreateTime(new Date());
        eventOverview.setUpdateTime(new Date());
        eventOverview.setOrganizerId((String) eventData.get("organizerId"));

        // 生成工单ID。这块不需要填写，等生成工单时生成工单然后修改
//        eventOverview.setWorkOrderId("WO" + System.currentTimeMillis());

        int result = eventOverviewMapper.insert(eventOverview);
        if (result > 0) {
            return eventOverview;
        } else {
            throw new RuntimeException("Failed to create event overview");
        }
    }

    @Override
    public EventOverview updateEventOverview(String id, Map<String, Object> eventData, String tenantId) {
        // 先查询现有记录
        EventOverview existingEvent = findEventOverviewById(id, tenantId);
        if (existingEvent == null) {
            throw new IllegalArgumentException("Event not found with id: " + id);
        }

        // 更新字段
        existingEvent.setTitle((String) eventData.get("title"));
        existingEvent.setType((String) eventData.get("type"));
        existingEvent.setTypeName((String) eventData.get("typeName"));
        existingEvent.setAddress((String) eventData.get("address"));
        existingEvent.setRemark((String) eventData.get("remark"));
        existingEvent.setCoordinate((String) eventData.get("coordinate"));
        existingEvent.setCoordinateName((String) eventData.get("coordinateName"));
        existingEvent.setUpdateTime(new Date());

        // 如果原状态是已驳回，更新时自动改为待审核状态，并清除驳回原因
        if (EventStatusEnums.REJECTED.equals(existingEvent.getStatus())) {
            existingEvent.setStatus(EventStatusEnums.PENDING_REVIEW);
            existingEvent.setRejectReason(null); // 清除驳回原因
            log.info("事件状态从已驳回改为待审核: {}", id);
        }
        QueryWrapper<EventOverview> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        if (StringUtils.hasText(tenantId)) {
            queryWrapper.eq("tenant_id", tenantId);
        }

        int result = eventOverviewMapper.update(existingEvent, queryWrapper);
        if (result > 0) {
            return existingEvent;
        } else {
            throw new RuntimeException("Failed to update event overview");
        }
    }

    @Override
    public boolean deleteEventOverview(String id, String tenantId) {
        QueryWrapper<EventOverview> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        if (StringUtils.hasText(tenantId)) {
            queryWrapper.eq("tenant_id", tenantId);
        }

        int result = eventOverviewMapper.delete(queryWrapper);
        return result > 0;
    }

    @Override
    public boolean updateEventCoordinate(String id, String coordinate, String coordinateName, String tenantId) {
        if (!isValidCoordinate(coordinate)) {
            log.warn("Invalid coordinate format: {}", coordinate);
            return false;
        }
        
        EventOverview eventOverview = new EventOverview();
        eventOverview.setId(id);
        eventOverview.setCoordinate(coordinate);
        eventOverview.setCoordinateName(coordinateName);
        eventOverview.setUpdateTime(new Date());
        
        QueryWrapper<EventOverview> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        if (StringUtils.hasText(tenantId)) {
            queryWrapper.eq("tenant_id", tenantId);
        }
        
        int result = eventOverviewMapper.update(eventOverview, queryWrapper);
        return result > 0;
    }

    @Override
    public int batchUpdateEventCoordinates(List<Map<String, String>> updates, String tenantId) {
        int successCount = 0;
        for (Map<String, String> update : updates) {
            String id = update.get("id");
            String coordinate = update.get("coordinate");
            String coordinateName = update.get("coordinateName");
            
            if (updateEventCoordinate(id, coordinate, coordinateName, tenantId)) {
                successCount++;
            }
        }
        return successCount;
    }

    @Override
    public Map<String, Object> getEventStatistics(String tenantId, String projectId) {
        return eventOverviewMapper.getEventStatistics(tenantId, projectId);
    }

    @Override
    public List<Map<String, Object>> getEventHotspotStatistics(String tenantId, int limit) {
        return eventOverviewMapper.getEventHotspotStatistics(tenantId, limit);
    }

    @Override
    public List<Map<String, Object>> getEventTrendStatistics(String tenantId, int days) {
        return eventOverviewMapper.getEventTrendStatistics(tenantId, days);
    }

    @Override
    public List<Map<String, Object>> getEventStatusDistribution(String tenantId, String projectId) {
        return eventOverviewMapper.getEventStatusDistribution(tenantId, projectId);
    }

    @Override
    public Map<String, Object> getProcessEfficiencyStatistics(String tenantId, int days) {
        return eventOverviewMapper.getProcessEfficiencyStatistics(tenantId, days);
    }

    @Override
    public String geocodeAddress(String address) {
        // TODO: 集成地理编码服务（如高德地图API）
        log.info("Geocoding address: {}", address);
        return null;
    }

    @Override
    public String reverseGeocodeCoordinate(String coordinate) {
        // TODO: 集成逆地理编码服务
        log.info("Reverse geocoding coordinate: {}", coordinate);
        return null;
    }

    @Override
    public boolean isValidCoordinate(String coordinate) {
        if (!StringUtils.hasText(coordinate)) {
            return false;
        }
        
        try {
            String[] parts = coordinate.split(",");
            if (parts.length != 2) {
                return false;
            }
            
            double longitude = Double.parseDouble(parts[0].trim());
            double latitude = Double.parseDouble(parts[1].trim());
            
            // 检查经纬度范围
            return longitude >= -180 && longitude <= 180 && latitude >= -90 && latitude <= 90;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    @Override
    public double calculateDistance(String coordinate1, String coordinate2) {
        if (!isValidCoordinate(coordinate1) || !isValidCoordinate(coordinate2)) {
            return -1;
        }
        
        try {
            String[] coord1 = coordinate1.split(",");
            String[] coord2 = coordinate2.split(",");
            
            double lon1 = Double.parseDouble(coord1[0].trim());
            double lat1 = Double.parseDouble(coord1[1].trim());
            double lon2 = Double.parseDouble(coord2[0].trim());
            double lat2 = Double.parseDouble(coord2[1].trim());
            
            return calculateHaversineDistance(lat1, lon1, lat2, lon2);
        } catch (Exception e) {
            log.error("Error calculating distance", e);
            return -1;
        }
    }

    @Override
    public List<EventOverview> findEventsInRadius(String centerCoordinate, double radiusMeters, String tenantId) {
        QueryWrapper<EventOverview> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", tenantId);
        queryWrapper.isNotNull("coordinate");
        queryWrapper.ne("coordinate", "");
        
        List<EventOverview> allEvents = eventOverviewMapper.selectList(queryWrapper);
        
        return allEvents.stream()
                .filter(event -> {
                    double distance = calculateDistance(centerCoordinate, event.getCoordinate());
                    return distance >= 0 && distance <= radiusMeters;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<EventOverview> exportEventOverview(EventOverviewPageRequest request) {
        QueryWrapper<EventOverview> queryWrapper = buildQueryWrapper(request);
        
        // 设置排序
        if (StringUtils.hasText(request.getOrderBy())) {
            String orderBy = convertOrderByField(request.getOrderBy());
            if ("DESC".equalsIgnoreCase(request.getOrderDirection())) {
                queryWrapper.orderByDesc(orderBy);
            } else {
                queryWrapper.orderByAsc(orderBy);
            }
        } else {
            queryWrapper.orderByDesc("create_time");
        }
        
        return eventOverviewMapper.selectList(queryWrapper);
    }

    @Override
    public Map<String, Object> getProcessDurationStatistics(String tenantId, int days) {
        return eventOverviewMapper.getProcessDurationStatistics(tenantId, days);
    }

    @Override
    public List<EventOverview> getOverdueEvents(String tenantId, int limit) {
        return eventOverviewMapper.getOverdueEvents(tenantId, limit);
    }

    @Override
    public List<EventOverview> getHighPriorityEvents(String tenantId, int limit) {
        return eventOverviewMapper.getHighPriorityEvents(tenantId, limit);
    }

    /**
     * 构建查询条件
     */
    private QueryWrapper<EventOverview> buildQueryWrapper(EventOverviewPageRequest request) {
        QueryWrapper<EventOverview> queryWrapper = new QueryWrapper<>();
        
        // 租户过滤
        if (StringUtils.hasText(request.getTenantId())) {
            queryWrapper.eq("tenant_id", request.getTenantId());
        }
        
        // 事件标题过滤
        if (StringUtils.hasText(request.getTitle())) {
            queryWrapper.like("title", request.getTitle());
        }

        // 事件类型过滤
        if (StringUtils.hasText(request.getType())) {
            queryWrapper.eq("type", request.getType());
        }

        // 状态过滤
        if (StringUtils.hasText(request.getStatus())) {
            queryWrapper.eq("status", request.getStatus());
        }
        
        // 时间范围过滤
        if (request.getCreateTimeFrom() != null) {
            queryWrapper.ge("create_time", request.getCreateTimeFrom());
        }
        
        if (request.getCreateTimeTo() != null) {
            queryWrapper.le("create_time", request.getCreateTimeTo());
        }
        

        
        return queryWrapper;
    }
    
    /**
     * 转换排序字段名
     */
    private String convertOrderByField(String orderBy) {
        // 将驼峰命名转换为下划线命名
        return orderBy.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
    }
    

    
    /**
     * 计算两点间距离（Haversine公式）
     */
    private double calculateHaversineDistance(double lat1, double lon1, double lat2, double lon2) {
        final double R = 6371000; // 地球半径（米）
        
        double dLat = Math.toRadians(lat2 - lat1);
        double dLon = Math.toRadians(lon2 - lon1);
        
        double a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2)) *
                        Math.sin(dLon / 2) * Math.sin(dLon / 2);
        
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return R * c;
    }

    @Override
    public void approveEvent(String tenantId, String eventId) {
        try {
            // 查找事件
            EventOverview eventOverview = findEventOverviewById(eventId, tenantId);
            if (eventOverview == null) {
                throw new IllegalArgumentException("事件不存在");
            }

            // 检查状态
            if (!EventStatusEnums.PENDING_REVIEW.equals(eventOverview.getStatus())) {
                throw new IllegalArgumentException("只有待审核状态的事件才能审核通过");
            }

            // 更新状态为已分派
            eventOverview.setStatus(EventStatusEnums.ASSIGNED);
            eventOverview.setUpdateTime(new Date());

            QueryWrapper<EventOverview> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("id", eventId);
            queryWrapper.eq("tenant_id", tenantId);

            int result = eventOverviewMapper.update(eventOverview, queryWrapper);
            if (result <= 0) {
                throw new RuntimeException("更新事件状态失败");
            }

            log.info("事件审核通过并派单成功: {}", eventId);
        } catch (Exception e) {
            log.error("审核通过事件失败: {}", eventId, e);
            throw new RuntimeException("审核通过事件失败", e);
        }
    }

    @Override
    public void rejectEvent(String tenantId, String eventId, String reason) {
        try {
            // 查找事件
            EventOverview eventOverview = findEventOverviewById(eventId, tenantId);
            if (eventOverview == null) {
                throw new IllegalArgumentException("事件不存在");
            }

            // 检查状态
            if (!EventStatusEnums.PENDING_REVIEW.equals(eventOverview.getStatus())) {
                throw new IllegalArgumentException("只有待审核状态的事件才能驳回");
            }

            // 更新状态为已驳回
            eventOverview.setStatus(EventStatusEnums.REJECTED);
            eventOverview.setUpdateTime(new Date());

            // 设置驳回原因到专门的字段
            eventOverview.setRejectReason(reason);

            QueryWrapper<EventOverview> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("id", eventId);
            queryWrapper.eq("tenant_id", tenantId);

            int result = eventOverviewMapper.update(eventOverview, queryWrapper);
            if (result <= 0) {
                throw new RuntimeException("更新事件状态失败");
            }

            log.info("事件驳回成功: {}, 原因: {}", eventId, reason);
        } catch (Exception e) {
            log.error("驳回事件失败: {}", eventId, e);
            throw new RuntimeException("驳回事件失败", e);
        }
    }

    @Override
    public void completeEvent(String tenantId, String eventId) {
        try {
            // 查找事件
            EventOverview eventOverview = findEventOverviewById(eventId, tenantId);
            if (eventOverview == null) {
                throw new IllegalArgumentException("事件不存在");
            }

            // 检查状态
            if (!EventStatusEnums.PENDING_REVIEW.equals(eventOverview.getStatus())) {
                throw new IllegalArgumentException("只有待审核状态的事件才能直接完成");
            }

            // 更新状态为已完成
            eventOverview.setStatus(EventStatusEnums.COMPLETED);
            eventOverview.setUpdateTime(new Date());

            QueryWrapper<EventOverview> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("id", eventId);
            queryWrapper.eq("tenant_id", tenantId);

            int result = eventOverviewMapper.update(eventOverview, queryWrapper);
            if (result <= 0) {
                throw new RuntimeException("更新事件状态失败");
            }

            log.info("事件直接完成成功: {}", eventId);
        } catch (Exception e) {
            log.error("直接完成事件失败: {}", eventId, e);
            throw new RuntimeException("直接完成事件失败", e);
        }
    }
}
