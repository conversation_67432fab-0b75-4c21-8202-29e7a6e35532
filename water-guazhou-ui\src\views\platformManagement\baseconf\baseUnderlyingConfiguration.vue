<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      class="card-table"
      :config="TableConfig"
    ></CardTable>
    <DialogForm
      ref="refDialogForm"
      :config="DialogFormConfig"
    ></DialogForm>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue'
import { 
  getUnderlyingConfigList,
  addUnderlyingConfig,
  editUnderlyingConfig,
  deleteUnderlyingConfig,
  getUnderlyingConfigDetail
} from '@/api/platformManagement/baseUnderlyingConfiguration'
import { SLConfirm, SLMessage } from '@/utils/Message'

const refSearch = ref()
const refDialogForm = ref()

const SearchConfig = reactive({
  labelWidth: '100px',
  filters: [
    { 
      type: 'input', 
      label: '主机地址', 
      field: 'hostAddress', 
      placeholder: '请输入主机地址',
      onChange: () => refreshData() 
    },
    {
      type: 'btn-group',
      btns: [
        { type: 'primary', perm: true, text: '查询', click: () => refreshData() },
        { perm: true, type: 'primary', text: '新增', click: () => handleAdd() },
        { perm: true, type: 'danger', text: '批量删除', click: () => handleDelete() }
      ]
    }
  ],
  defaultParams: {}
})

const TableConfig = reactive({
  columns: [
    { label: '主机地址', prop: 'hostAddress' },
    { label: '端口号', prop: 'hostPort' },
    { label: '数据库名称', prop: 'name' },
    { label: '用户名', prop: 'username' },
    { label: 'url扩展参数', prop: 'urlEx' },
    { label: '连接池配置', prop: 'poolConfig' },
    { label: '安全配置', prop: 'safeConfig' },
    { label: '超时与重试', prop: 'connTimeout' },
    { label: '唯一站点id', prop: 'siteId' },
    { label: 'IP地址', prop: 'ipAddress' },
    { label: '数据库版本', prop: 'dbVersion' }
  ],
  dataList: [],
  operations: [
    {
      perm: true,
      type: 'primary',
      isTextBtn: true,
      text: '查看详情',
      click: (row) => handleDetail(row)
    },
    {
      perm: true,
      type: 'primary',
      isTextBtn: true,
      text: '编辑',
      click: (row) => handleAdd(row)
    },
    {
      perm: true,
      type: 'danger',
      isTextBtn: true,
      text: '删除',
      click: (row) => handleDelete(row)
    }
  ],
  pagination: {
    total: 0,
    page: 1,
    align: 'right',
    limit: 20,
    handlePage: (page) => {
      TableConfig.pagination.page = page
      refreshData()
    },
    handleSize: (size) => {
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  handleSelectChange: (rows) => {
    TableConfig.selectList = rows || []
  }
})

const DialogFormConfig = reactive({
  title: '新增基础配置',
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '主机地址',
          field: 'hostAddress',
          rules: [{ required: true, message: '请输入主机地址' }]
        },
        {
          type: 'input',
          label: '端口号',
          field: 'hostPort',
          rules: [{ required: true, message: '请输入端口号' }]
        },
        {
          type: 'input',
          label: '数据库名称',
          field: 'name',
          rules: [{ required: true, message: '请输入数据库名称' }]
        },
        {
          type: 'input',
          label: '用户名',
          field: 'username',
          rules: [{ required: true, message: '请输入用户名' }]
        },
        {
          type: 'input',
          label: '密码',
          field: 'password',
          rules: [{ required: true, message: '请输入密码' }]
        },
        {
          type: 'input',
          label: 'url扩展参数',
          field: 'urlEx',
          rules: [{ required: true, message: '请输入url扩展参数' }]
        },
        {
          type: 'input',
          label: '连接池配置',
          field: 'poolConfig',
          rules: [{ required: true, message: '请输入连接池配置' }]
        },
        {
          type: 'input',
          label: '安全配置',
          field: 'safeConfig',
          rules: [{ required: true, message: '请输入安全配置' }]
        },
        {
          type: 'input',
          label: '超时与重试',
          field: 'connTimeout',
          rules: [{ required: true, message: '请输入超时与重试' }]
        },
        {
          type: 'input',
          label: '唯一站点id',
          field: 'siteId',
          rules: [{ required: true, message: '请输入唯一站点id' }]
        },
        {
          type: 'input',
          label: 'IP地址',
          field: 'ipAddress',
          rules: [{ required: true, message: '请输入IP地址' }]
        },
        {
          type: 'input',
          label: '数据库版本',
          field: 'dbVersion',
          rules: [{ required: true, message: '请输入数据库版本' }]
        }
      ]
    }
  ],
  labelPosition: 'top',
  defaultValue: {},
  dialogWidth: 600,
  draggable: true,
  showSubmit: true,
  showCancel: true,
  cancelText: '取消',
  submitText: '确定',
  submit: async (params) => {
    try {
      if (params.id) {
        await editUnderlyingConfig(params)
        SLMessage.success('修改成功')
      } else {
        await addUnderlyingConfig(params)
        SLMessage.success('新增成功')
      }
      refDialogForm.value?.closeDialog()
      refreshData()
    } catch (error) {
      SLMessage.error('操作失败')
    }
  }
})

// 重置对话框配置
const resetDialogConfig = () => {
  DialogFormConfig.group[0].fields.forEach(field => {
    field.disabled = false
    field.readonly = false
  })
  
  DialogFormConfig.showSubmit = true
  DialogFormConfig.showCancel = true
  DialogFormConfig.cancelText = '取消'
  DialogFormConfig.submitText = '确定'
  
  DialogFormConfig.submit = async (params) => {
    try {
      if (params.id) {
        await editUnderlyingConfig(params)
        SLMessage.success('修改成功')
      } else {
        await addUnderlyingConfig(params)
        SLMessage.success('新增成功')
      }
      refDialogForm.value?.closeDialog()
      refreshData()
    } catch (error) {
      SLMessage.error('操作失败')
    }
  }
}

// 查看详情
const handleDetail = async (row) => {
  try {
    const res = await getUnderlyingConfigDetail(row.id)
    const detailData = res.data?.data || res
    
    resetDialogConfig()
    DialogFormConfig.title = '基础配置详情'
    DialogFormConfig.defaultValue = { ...detailData }
    DialogFormConfig.group[0].fields.forEach(field => {
      field.disabled = true
    })
    DialogFormConfig.showSubmit = false
    DialogFormConfig.cancelText = '关闭'
    refDialogForm.value?.openDialog()
  } catch (error) {
    SLMessage.error('获取详情失败')
  }
}

// 新增/编辑
const handleAdd = (row) => {
  resetDialogConfig()
  
  if (row) {
    DialogFormConfig.title = '编辑基础配置'
    DialogFormConfig.defaultValue = { ...row }
  } else {
    DialogFormConfig.title = '新增基础配置'
    DialogFormConfig.defaultValue = {}
  }
  
  refDialogForm.value?.openDialog()
}

// 删除
const handleDelete = async (row) => {
  try {
    const ids = row ? [row.id] : TableConfig.selectList.map(item => item.id)
    if (!ids.length) {
      SLMessage.warning('请选择要删除的数据')
      return
    }
    
    await SLConfirm('确定要删除选中的数据吗？')
    await deleteUnderlyingConfig(ids)
    SLMessage.success('删除成功')
    refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      SLMessage.error('删除失败')
    }
  }
}

// 刷新数据
const refreshData = async () => {
  try {
    const res = await getUnderlyingConfigList({
      page: TableConfig.pagination.page,
      size: TableConfig.pagination.limit,
      ...(refSearch.value?.queryParams || {})
    })
    const responseData = res.data?.data || res
    TableConfig.dataList = responseData.records || responseData
    TableConfig.pagination.total = responseData.total || responseData.length || 0
  } catch (error) {
    SLMessage.error('数据加载失败')
  }
}

onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.wrapper {
  padding: 20px;
}

.card-table {
  margin-top: 20px;
}
</style>