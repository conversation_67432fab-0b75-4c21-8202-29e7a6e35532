import { request } from '@/plugins/axios'

// 获取模型类型列表
export function getTemplateTypeList(params: any) {
  return request({
    url: '/api/base/template/type/list',
    method: 'get',
    params
  })
}

// 获取模型类型详情
export function getTemplateTypeDetail(id: string) {
  return request({
    url: '/api/base/template/type/getDetail',
    method: 'get',
    params: { id }
  })
}

// 新增模型类型配置
export function addTemplateType(data: any) {
  return request({
    url: '/api/base/template/type/add',
    method: 'post',
    data
  })
}

// 修改模型类型配置
export function editTemplateType(data: any) {
  return request({
    url: '/api/base/template/type/edit',
    method: 'post',
    data
  })
}

// 删除模型类型配置
export function deleteTemplateType(ids: string[]) {
  return request({
    url: '/api/base/template/type/deleteIds',
    method: 'delete',
    data: ids
  })
} 