package org.thingsboard.server.dao.waterSource;

import org.thingsboard.server.dao.model.sql.conservationWaterLevel.ConservationAnalysis;
import org.thingsboard.server.dao.model.sql.conservationWaterLevel.ConservationWaterLevel;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 涵养水位服务接口
 */
public interface ConservationWaterLevelService {

    /**
     * 保存涵养水位数据
     */
    ConservationWaterLevel saveWaterLevel(ConservationWaterLevel entity);

    /**
     * 更新涵养水位数据
     */
    ConservationWaterLevel updateWaterLevel(ConservationWaterLevel entity);

    /**
     * 删除涵养水位数据
     */
    void deleteWaterLevel(String id);

    /**
     * 根据ID获取涵养水位数据
     */
    ConservationWaterLevel getWaterLevelById(String id);

    /**
     * 分页查询涵养水位数据
     */
    Map<String, Object> getWaterLevelList(Map<String, Object> params);

    /**
     * 获取水位变化数据
     */
    List<ConservationWaterLevel> getWaterLevelChangeData(String stationId, Date startTime, Date endTime);

    /**
     * 获取测点最新水位数据
     */
    ConservationWaterLevel getLatestWaterLevel(String stationId);

    /**
     * 获取统计数据
     */
    Map<String, Object> getStatisticsData(String stationId, Date startTime, Date endTime);

    /**
     * 批量导入水位数据
     */
    void batchImportWaterLevel(List<ConservationWaterLevel> list);

    /**
     * 执行智能分析
     */
    ConservationAnalysis performIntelligentAnalysis(String stationId, Date startTime, Date endTime, String tenantId, String creator);

    /**
     * 获取分析结果列表
     */
    Map<String, Object> getAnalysisList(Map<String, Object> params);

    /**
     * 根据ID获取分析结果
     */
    ConservationAnalysis getAnalysisById(String id);

    /**
     * 获取最新分析结果
     */
    ConservationAnalysis getLatestAnalysis(String stationId);

    /**
     * 获取风险等级统计
     */
    List<Map<String, Object>> getRiskLevelStatistics(String tenantId);

    /**
     * 获取涵养潜力趋势
     */
    List<Map<String, Object>> getConservationPotentialTrend(String stationId, Date startTime, Date endTime);

    /**
     * 重新分析
     */
    ConservationAnalysis reAnalysis(String analysisId, String creator);

    /**
     * 删除分析结果
     */
    void deleteAnalysis(String id);
}
