import { request } from '@/plugins/axios'

// 获取数据库升级记录列表
export function getBaseDatabaseUpgradeList(params: any) {
  return request({
    url: '/api/base/database/upgrade/list',
    method: 'get',
    params
  })
}

// 获取数据库升级记录详情
export function getBaseDatabaseUpgradeDetail(id: string) {
  return request({
    url: '/api/base/database/upgrade/getDetail',
    method: 'get',
    params: { id }
  })
}

// 新增数据库升级记录配置
export function addBaseDatabaseUpgrade(data: any) {
  return request({
    url: '/api/base/database/upgrade/add',
    method: 'post',
    data
  })
}

// 修改数据库升级记录配置
export function editBaseDatabaseUpgrade(data: any) {
  return request({
    url: '/api/base/database/upgrade/edit',
    method: 'post',
    data
  })
}

// 删除数据库升级记录配置
export function deleteBaseDatabaseUpgrade(ids: string[]) {
  return request({
    url: '/api/base/database/upgrade/deleteIds',
    method: 'delete',
    data: ids
  })
} 