package org.thingsboard.server.dao.orderWork;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.mapper.workOrder.EventOverviewMapper;
import org.thingsboard.server.dao.model.sql.workOrder.EventOverview;
import org.thingsboard.server.dao.model.sql.workOrder.EventStatusEnums;
import org.thingsboard.server.dao.sql.workOrder.WorkOrderTypeMapper;

import java.util.*;

/**
 * 维修总览服务实现
 */
@Service
public class RepairOverviewServiceImpl implements RepairOverviewService {

    @Autowired
    private EventOverviewMapper eventOverviewMapper;

    @Autowired
    private WorkOrderTypeMapper workOrderTypeMapper;

    @Override
    public Map<String, Object> getStatistics(JSONObject params) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取月度统计数据
            List<Map<String, Object>> monthlyData = getMonthlyStatistics(params);
            result.put("monthlyData", monthlyData);
            
            // 获取汇总统计
            Map<String, Object> summary = getSummaryStatistics(params);
            result.put("summary", summary);
            
            // 获取维修记录（前10条）
            JSONObject recordParams = new JSONObject(params);
            recordParams.put("page", 1);
            recordParams.put("size", 10);
            Map<String, Object> recordsResult = getRecords(recordParams);
            result.put("repairRecords", recordsResult.get("data"));
            
        } catch (Exception e) {
            // 返回空数据
            result.put("monthlyData", new ArrayList<>());
            result.put("summary", new HashMap<>());
            result.put("repairRecords", new ArrayList<>());
        }
        
        return result;
    }

    @Override
    public Map<String, Object> getRecords(JSONObject params) {
        Map<String, Object> result = new HashMap<>();

        try {
            String tenantId = params.getString("tenantId");
            int page = params.getIntValue("page");
            int size = params.getIntValue("size");
            String year = params.getString("year");
            String repairType = params.getString("repairType");
            String repairStatus = params.getString("repairStatus");

            // 构建查询条件
            QueryWrapper<EventOverview> queryWrapper = new QueryWrapper<>();

            queryWrapper.eq("tenant_id", tenantId);

            // 添加年份过滤
            if (year != null && !year.isEmpty()) {
                queryWrapper.apply("EXTRACT(YEAR FROM create_time) = {0}", year);
            }

            // 添加维修类型过滤
            if (repairType != null && !repairType.isEmpty()) {
                queryWrapper.like("type_name", repairType);
            }

            // 添加状态过滤
            if (repairStatus != null && !repairStatus.isEmpty()) {
                queryWrapper.eq("status", convertStatusToEnum(repairStatus));
            }

            // 排序
            queryWrapper.orderByDesc("create_time");

            // 分页查询
            Page<EventOverview> pageQuery = new Page<>(page, size);

            IPage<EventOverview> pageResult = eventOverviewMapper.selectPage(pageQuery, queryWrapper);

            // 转换为返回格式
            List<Map<String, Object>> records = new ArrayList<>();
            for (EventOverview event : pageResult.getRecords()) {
                Map<String, Object> record = new HashMap<>();
                record.put("id", event.getId());
                record.put("deviceName", event.getTitle() != null ? event.getTitle() : "维修事件");
                record.put("location", event.getAddress() != null ? event.getAddress() : "未知地址");
                record.put("repairType", event.getTypeName() != null ? event.getTypeName() : getRepairTypeByEventType(event.getType()));
                record.put("repairStatus", convertEventStatusToString(event.getStatus()));
                record.put("createTime", event.getCreateTime());
                record.put("creator", event.getOrganizerId() != null ? event.getOrganizerId() : "系统");
                record.put("duration", calculateDuration(event.getCreateTime(), event.getUpdateTime()));
                records.add(record);
            }

            result.put("data", records);
            result.put("total", pageResult.getTotal());

        } catch (Exception e) {
            // 返回空数据
            result.put("data", new ArrayList<>());
            result.put("total", 0);
        }

        return result;
    }

    @Override
    public String exportData(JSONObject params) {
        // 导出功能实现
        return "导出成功";
    }

    @Override
    public List<Map<String, Object>> getRepairTypes(String tenantId) {
        List<Map<String, Object>> types = new ArrayList<>();

        try {
            // 使用Mapper查询工单类型
            List<org.thingsboard.server.dao.model.sql.workOrder.WorkOrderType> workOrderTypes =
                workOrderTypeMapper.findList("1", tenantId);

            for (org.thingsboard.server.dao.model.sql.workOrder.WorkOrderType type : workOrderTypes) {
                Map<String, Object> typeMap = new HashMap<>();
                typeMap.put("label", type.getName());
                typeMap.put("value", type.getName());
                types.add(typeMap);
            }
        } catch (Exception e) {
            // 返回默认类型
            types = getDefaultRepairTypes();
        }

        return types;
    }

    @Override
    public List<Map<String, Object>> getRepairStatuses() {
        List<Map<String, Object>> statuses = new ArrayList<>();
        
        Map<String, Object> status1 = new HashMap<>();
        status1.put("label", "待办");
        status1.put("value", "待办");
        statuses.add(status1);
        
        Map<String, Object> status2 = new HashMap<>();
        status2.put("label", "已办");
        status2.put("value", "已办");
        statuses.add(status2);
        
        Map<String, Object> status3 = new HashMap<>();
        status3.put("label", "处理中");
        status3.put("value", "处理中");
        statuses.add(status3);
        
        return statuses;
    }

    /**
     * 获取月度统计数据
     */
    private List<Map<String, Object>> getMonthlyStatistics(JSONObject params) {
        List<Map<String, Object>> monthlyData = new ArrayList<>();

        try {
            String tenantId = params.getString("tenantId");
            String year = params.getString("year");
            if (year == null || year.isEmpty()) {
                year = String.valueOf(Calendar.getInstance().get(Calendar.YEAR));
            }

            // 构建查询条件
            QueryWrapper<EventOverview> queryWrapper = new QueryWrapper<>();

            queryWrapper.eq("tenant_id", tenantId);
            queryWrapper.apply("EXTRACT(YEAR FROM create_time) = {0}", year);
            queryWrapper.orderByAsc("create_time");

            // 查询事件数据
            List<EventOverview> events = eventOverviewMapper.selectList(queryWrapper);

            // 按月份统计
            Map<Integer, Map<String, Integer>> monthlyStats = new HashMap<>();
            for (int i = 1; i <= 12; i++) {
                Map<String, Integer> stats = new HashMap<>();
                stats.put("waterSupplyIssues", 0);
                stats.put("heatSupplyIssues", 0);
                stats.put("pipelineMaintenance", 0);
                stats.put("valveMaintenance", 0);
                monthlyStats.put(i, stats);
            }

            // 统计每个事件
            for (EventOverview event : events) {
                Calendar cal = Calendar.getInstance();
                cal.setTime(event.getCreateTime());
                int month = cal.get(Calendar.MONTH) + 1;

                String category = categorizeEvent(event);
                Map<String, Integer> stats = monthlyStats.get(month);
                stats.put(category, stats.get(category) + 1);
            }

            // 转换为返回格式
            for (int i = 1; i <= 12; i++) {
                Map<String, Object> monthData = new HashMap<>();
                monthData.put("month", i + "月");
                Map<String, Integer> stats = monthlyStats.get(i);
                monthData.put("waterSupplyIssues", stats.get("waterSupplyIssues"));
                monthData.put("heatSupplyIssues", stats.get("heatSupplyIssues"));
                monthData.put("pipelineMaintenance", stats.get("pipelineMaintenance"));
                monthData.put("valveMaintenance", stats.get("valveMaintenance"));
                monthlyData.add(monthData);
            }

        } catch (Exception e) {
            // 返回空的月度数据
            for (int i = 1; i <= 12; i++) {
                Map<String, Object> monthData = new HashMap<>();
                monthData.put("month", i + "月");
                monthData.put("waterSupplyIssues", 0);
                monthData.put("heatSupplyIssues", 0);
                monthData.put("pipelineMaintenance", 0);
                monthData.put("valveMaintenance", 0);
                monthlyData.add(monthData);
            }
        }

        return monthlyData;
    }

    /**
     * 获取汇总统计数据
     */
    private Map<String, Object> getSummaryStatistics(JSONObject params) {
        Map<String, Object> summary = new HashMap<>();

        try {
            String tenantId = params.getString("tenantId");
            String year = params.getString("year");
            if (year == null || year.isEmpty()) {
                year = String.valueOf(Calendar.getInstance().get(Calendar.YEAR));
            }

            // 构建查询条件
            QueryWrapper<EventOverview> queryWrapper = new QueryWrapper<>();

            queryWrapper.eq("tenant_id", tenantId);
            queryWrapper.apply("EXTRACT(YEAR FROM create_time) = {0}", year);

            // 查询事件数据
            List<EventOverview> events = eventOverviewMapper.selectList(queryWrapper);

            // 统计各类型数量
            int waterSupplyTotal = 0;
            int heatSupplyTotal = 0;
            int pipelineTotal = 0;
            int valveTotal = 0;

            for (EventOverview event : events) {
                String category = categorizeEvent(event);
                switch (category) {
                    case "waterSupplyIssues":
                        waterSupplyTotal++;
                        break;
                    case "heatSupplyIssues":
                        heatSupplyTotal++;
                        break;
                    case "pipelineMaintenance":
                        pipelineTotal++;
                        break;
                    case "valveMaintenance":
                        valveTotal++;
                        break;
                }
            }

            summary.put("waterSupplyTotal", waterSupplyTotal);
            summary.put("heatSupplyTotal", heatSupplyTotal);
            summary.put("pipelineTotal", pipelineTotal);
            summary.put("valveTotal", valveTotal);

        } catch (Exception e) {
            // 返回空的汇总数据
            summary.put("waterSupplyTotal", 0);
            summary.put("heatSupplyTotal", 0);
            summary.put("pipelineTotal", 0);
            summary.put("valveTotal", 0);
        }

        return summary;
    }

    /**
     * 获取默认维修类型
     */
    private List<Map<String, Object>> getDefaultRepairTypes() {
        List<Map<String, Object>> types = new ArrayList<>();
        String[] typeNames = {"水表问题", "热表设备", "管网维修", "阀门维修", "金属维修", "管道维修"};
        
        for (String typeName : typeNames) {
            Map<String, Object> type = new HashMap<>();
            type.put("label", typeName);
            type.put("value", typeName);
            types.add(type);
        }
        
        return types;
    }

    /**
     * 根据事件信息分类
     */
    private String categorizeEvent(EventOverview event) {
        String title = event.getTitle();
        String typeName = event.getTypeName();
        String type = event.getType();

        // 根据标题分类
        if (title != null) {
            if (title.contains("水表") || title.contains("水供") || title.contains("供水")) {
                return "waterSupplyIssues";
            } else if (title.contains("热表") || title.contains("热供") || title.contains("供热")) {
                return "heatSupplyIssues";
            } else if (title.contains("管网") || title.contains("管道") || title.contains("管线")) {
                return "pipelineMaintenance";
            } else if (title.contains("阀门") || title.contains("阀体")) {
                return "valveMaintenance";
            }
        }

        // 根据类型名称分类
        if (typeName != null) {
            if (typeName.contains("水表") || typeName.contains("供水")) {
                return "waterSupplyIssues";
            } else if (typeName.contains("热表") || typeName.contains("供热")) {
                return "heatSupplyIssues";
            } else if (typeName.contains("管网") || typeName.contains("管道")) {
                return "pipelineMaintenance";
            } else if (typeName.contains("阀门")) {
                return "valveMaintenance";
            }
        }

        // 根据类型ID分类
        if ("1".equals(type)) {
            return "waterSupplyIssues";
        } else if ("2".equals(type)) {
            return "heatSupplyIssues";
        } else if ("3".equals(type)) {
            return "pipelineMaintenance";
        } else {
            return "valveMaintenance";
        }
    }

    /**
     * 根据事件类型获取维修类型名称
     */
    private String getRepairTypeByEventType(String eventType) {
        if ("1".equals(eventType)) {
            return "水表维修";
        } else if ("2".equals(eventType)) {
            return "热表维修";
        } else if ("3".equals(eventType)) {
            return "管道维修";
        } else if ("4".equals(eventType)) {
            return "阀门维修";
        } else {
            return "设备维修";
        }
    }

    /**
     * 将事件状态转换为字符串
     */
    private String convertEventStatusToString(org.thingsboard.server.dao.model.sql.workOrder.EventStatusEnums status) {
        if (status == null) {
            return "未知";
        }

        switch (status) {
            case PENDING_REVIEW:
            case ASSIGNED:
                return "待办";
            case COMPLETED:
                return "已办";
            case PROCESSING:
                return "处理中";
            case REJECTED:
                return "已驳回";
            default:
                return "未知";
        }
    }

    /**
     * 将状态字符串转换为事件状态枚举
     */
    private org.thingsboard.server.dao.model.sql.workOrder.EventStatusEnums convertStatusToEnum(String status) {
        if ("待办".equals(status)) {
            return org.thingsboard.server.dao.model.sql.workOrder.EventStatusEnums.PENDING_REVIEW;
        } else if ("已办".equals(status)) {
            return org.thingsboard.server.dao.model.sql.workOrder.EventStatusEnums.COMPLETED;
        } else if ("处理中".equals(status)) {
            return org.thingsboard.server.dao.model.sql.workOrder.EventStatusEnums.PROCESSING;
        } else if ("已驳回".equals(status)) {
            return org.thingsboard.server.dao.model.sql.workOrder.EventStatusEnums.REJECTED;
        } else {
            return org.thingsboard.server.dao.model.sql.workOrder.EventStatusEnums.PENDING_REVIEW;
        }
    }



    /**
     * 计算耗时（小时）
     */
    private int calculateDuration(Date createTime, Date updateTime) {
        if (createTime == null) {
            return 0;
        }

        Date endTime = updateTime != null ? updateTime : new Date();
        long diffInMillis = endTime.getTime() - createTime.getTime();
        return (int) (diffInMillis / (1000 * 60 * 60)); // 转换为小时
    }
}
