package org.thingsboard.server.dao.deviceAuth;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.deviceAuth.DeviceUserAuth;
import org.thingsboard.server.dao.util.imodel.query.deviceAuth.DeviceUserAuthBatchSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.deviceAuth.DeviceUserAuthPageRequest;
import org.thingsboard.server.dao.util.imodel.query.deviceAuth.DeviceUserAuthSaveRequest;

import java.util.List;

/**
 * 设备用户权限关联服务接口
 */
public interface DeviceUserAuthService {

    /**
     * 分页查询设备用户权限关联
     */
    IPage<DeviceUserAuth> findByPage(DeviceUserAuthPageRequest request);

    /**
     * 根据设备ID查询设备用户权限关联
     */
    List<DeviceUserAuth> findByDeviceId(String deviceId, String tenantId);

    /**
     * 根据用户ID查询设备用户权限关联
     */
    List<DeviceUserAuth> findByUserId(String userId, String tenantId);

    /**
     * 保存设备用户权限关联
     */
    DeviceUserAuth save(DeviceUserAuthSaveRequest request, String tenantId);

    /**
     * 批量保存设备用户权限关联
     */
    void batchSave(DeviceUserAuthBatchSaveRequest request, String tenantId);

    /**
     * 删除设备用户权限关联
     */
    boolean delete(String id);

    /**
     * 根据设备ID删除设备用户权限关联
     */
    void deleteByDeviceId(String deviceId, String tenantId);
}
