package org.thingsboard.server.dao.model.sql.deviceAuth;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;

import java.util.Date;

/**
 * 设备用户权限关联实体
 */
@Data
@TableName("tb_device_user_auth")
public class DeviceUserAuth {
    /**
     * ID
     */
    @TableId
    private String id;
    
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 设备名称
     */
    private String deviceName;
    
    /**
     * 设备编码
     */
    private String deviceSerial;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 用户名称
     */
    private String userName;
    
    /**
     * 权限类型（1:查看,2:控制,3:管理）
     */
    private Integer authType;
    
    /**
     * 创建人
     */
    @ParseUsername
    private String creator;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 租户ID
     */
    private String tenantId;
}
