<!-- 维修总览测试页面 -->
<template>
  <div style="padding: 20px;">
    <h2>维修总览功能测试</h2>
    
    <el-button @click="testAPI" type="primary">测试API调用</el-button>
    <el-button @click="testChart" type="success">测试图表渲染</el-button>
    
    <div v-if="apiResult" style="margin-top: 20px;">
      <h3>API测试结果：</h3>
      <pre>{{ JSON.stringify(apiResult, null, 2) }}</pre>
    </div>
    
    <div v-if="showChart" style="margin-top: 20px;">
      <h3>图表测试：</h3>
      <VChart :option="chartOption" style="height: 400px; width: 100%;"></VChart>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { GetRepairOverviewStatistics } from '@/api/workorder/repairOverview'
import { RepairOverviewBarOption } from './echart'

const apiResult = ref(null)
const showChart = ref(false)
const chartOption = ref(null)

const testAPI = async () => {
  try {
    const res = await GetRepairOverviewStatistics({ year: '2024' })
    apiResult.value = res.data
    console.log('API测试成功:', res.data)
  } catch (error) {
    console.error('API测试失败:', error)
    apiResult.value = { error: error.message }
  }
}

const testChart = () => {
  const mockData = [
    { month: '1月', waterSupplyIssues: 73, heatSupplyIssues: 20, pipelineMaintenance: 56, valveMaintenance: 43 },
    { month: '2月', waterSupplyIssues: 45, heatSupplyIssues: 15, pipelineMaintenance: 32, valveMaintenance: 28 },
    { month: '3月', waterSupplyIssues: 62, heatSupplyIssues: 18, pipelineMaintenance: 41, valveMaintenance: 35 }
  ]
  
  chartOption.value = RepairOverviewBarOption(mockData)
  showChart.value = true
  console.log('图表测试完成')
}
</script>
