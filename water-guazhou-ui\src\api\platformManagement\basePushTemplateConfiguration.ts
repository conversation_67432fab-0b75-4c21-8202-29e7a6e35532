import { request } from '@/plugins/axios'

// 获取推送模板配置列表
export function getPushTemplateConfigList(params: any) {
  return request({
    url: '/api/base/push/template/configuration/list',
    method: 'get',
    params
  })
}

// 获取推送模板配置详情
export function getPushTemplateConfigDetail(id: string) {
  return request({
    url: '/api/base/push/template/configuration/getDetail',
    method: 'get',
    params: { id }
  })
}

// 新增推送模板配置配置
export function addPushTemplateConfig(data: any) {
  return request({
    url: '/api/base/push/template/configuration/add',
    method: 'post',
    data
  })
}

// 修改推送模板配置配置
export function editPushTemplateConfig(data: any) {
  return request({
    url: '/api/base/push/template/configuration/edit',
    method: 'post',
    data
  })
}

// 删除推送模板配置配置
export function deletePushTemplateConfig(ids: string[]) {
  return request({
    url: '/api/base/push/template/configuration/deleteIds',
    method: 'delete',
    data: ids
  })
} 