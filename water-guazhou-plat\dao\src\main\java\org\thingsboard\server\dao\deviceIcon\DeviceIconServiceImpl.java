package org.thingsboard.server.dao.deviceIcon;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.deviceIcon.DeviceIcon;
import org.thingsboard.server.dao.sql.deviceIcon.DeviceIconMapper;
import org.thingsboard.server.dao.util.SqlDao;
import org.thingsboard.server.dao.util.imodel.query.deviceIcon.DeviceIconBatchSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.deviceIcon.DeviceIconPageRequest;
import org.thingsboard.server.dao.util.imodel.query.deviceIcon.DeviceIconSaveRequest;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 设备图标服务实现类
 */
@Service
@SqlDao
public class DeviceIconServiceImpl implements DeviceIconService {

    @Autowired
    private DeviceIconMapper deviceIconMapper;

    @Override
    public IPage<DeviceIcon> findByPage(DeviceIconPageRequest request) {
        return deviceIconMapper.findByPage(request);
    }

    @Override
    public DeviceIcon findById(String id) {
        return deviceIconMapper.selectById(id);
    }

    @Override
    public List<DeviceIcon> findByDeviceType(String deviceType, String tenantId) {
        return deviceIconMapper.findByDeviceType(deviceType, tenantId);
    }

    @Override
    public DeviceIcon findByDeviceTypeAndStatus(String deviceType, String deviceStatus, String tenantId) {
        return deviceIconMapper.findByDeviceTypeAndStatus(deviceType, deviceStatus, tenantId);
    }

    @Override
    public DeviceIcon save(DeviceIconSaveRequest request, String tenantId) {
        DeviceIcon entity = new DeviceIcon();
        BeanUtils.copyProperties(request, entity);

        if (entity.getId() == null || entity.getId().isEmpty()) {
            // 新增
            entity.setCreateTime(new Date());
            entity.setUpdateTime(new Date());
            entity.setTenantId(tenantId);
            entity.setCreator(tenantId.toString()); // 实际应用中应该是当前用户ID
            deviceIconMapper.insert(entity);
        } else {
            // 更新
            entity.setUpdateTime(new Date());
            deviceIconMapper.updateById(entity);
        }

        return entity;
    }

    @Override
    @Transactional
    public void batchSave(DeviceIconBatchSaveRequest request, String tenantId) {
        // 先删除该设备类型的所有图标
        deviceIconMapper.deleteByDeviceType(request.getDeviceType(), tenantId);

        // 批量新增
        if (request.getIconList() != null && !request.getIconList().isEmpty()) {
            List<DeviceIcon> entityList = new ArrayList<>();
            for (DeviceIconBatchSaveRequest.IconItem item : request.getIconList()) {
                DeviceIcon entity = new DeviceIcon();
                entity.setId(UUID.randomUUID().toString());
                entity.setDeviceType(request.getDeviceType());
                entity.setIconUrl(item.getIconUrl());
                entity.setDeviceStatus(item.getDeviceStatus());
                entity.setStatusColor(item.getStatusColor());
                entity.setCreateTime(new Date());
                entity.setUpdateTime(new Date());
                entity.setTenantId(tenantId);
                entity.setCreator(tenantId); // 实际应用中应该是当前用户ID
                entityList.add(entity);
            }

            if (!entityList.isEmpty()) {
                deviceIconMapper.batchInsert(entityList);
            }
        }
    }

    @Override
    public boolean delete(String id) {
        return deviceIconMapper.deleteById(id) > 0;
    }

    @Override
    public void deleteByDeviceType(String deviceType, String tenantId) {
        deviceIconMapper.deleteByDeviceType(deviceType, tenantId);
    }
}
