import Layout from '@/views/layout/frame/Layout.vue'

export default [
  // 工单管理
  {
    path: '/workorder',
    component: Layout,
    hidden: true,
    meta: {
      roles: ['CUSTOMER_USER', 'TENANT_ADMIN', 'TENANT_SUPPORT']
    },
    children: [
      {
        path: 'index',
        name: 'workorder',
        component: () => import('@/views/workorder/OrderManage.vue'),
        meta: {
          title: '工单管理',
          icon: 'iconfont icon-xunjianguanli',
          roles: ['CUSTOMER_USER', 'TENANT_ADMIN', 'TENANT_SUPPORT']
        }
      },
      {
        path: 'OrderDetail',
        name: 'OrderDetail',
        hidden: true,
        component: () => import('@/views/workorder/components/orderDetail.vue'),
        meta: {
          title: '工单详情',
          icon: 'iconfont icon-xunjianguanli',
          roles: ['CUSTOMER_USER', 'TENANT_ADMIN', 'TENANT_SUPPORT']
        }
      },
      {
        path: 'overview',
        name: 'overview',
        hidden: true,
        component: () => import('@/views/workorder/overview/overview.vue'),
        meta: {
          title: '工单总览',
          icon: 'iconfont icon-xunjianguanli',
          roles: ['CUSTOMER_USER', 'TENANT_ADMIN', 'TENANT_SUPPORT']
        }
      },
      {
        path: 'workorderstatistics',
        name: 'workorderstatistics',
        hidden: true,
        component: () => import('@/views/workorder/statistics/statistics.vue'),
        meta: {
          title: '工单统计',
          icon: 'iconfont icon-xunjianguanli',
          roles: ['CUSTOMER_USER', 'TENANT_ADMIN', 'TENANT_SUPPORT']
        }
      },
      {
        path: 'repairOverview',
        name: 'repairOverview',
        hidden: true,
        component: () => import('@/views/workorder/repairOverview/index.vue'),
        meta: {
          title: '维修总览',
          icon: 'iconfont icon-xunjianguanli',
          roles: ['CUSTOMER_USER', 'TENANT_ADMIN', 'TENANT_SUPPORT']
        }
      }
    ]
  }
]
