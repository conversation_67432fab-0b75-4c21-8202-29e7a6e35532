import { request } from '@/plugins/axios'

// 获取画板管理列表
export function getDrawingBoardList(params: any) {
  return request({
    url: '/api/base/drawing/management/list',
    method: 'get',
    params
  })
}

// 获取画板管理详情
export function getDrawingBoardDetail(id: string) {
  return request({
    url: '/api/base/drawing/management/getDetail',
    method: 'get',
    params: { id }
  })
}

// 新增画板管理配置
export function addDrawingBoard(data: any) {
  return request({
    url: '/api/base/drawing/management/add',
    method: 'post',
    data
  })
}

// 修改画板管理配置
export function editDrawingBoard(data: any) {
  return request({
    url: '/api/base/drawing/management/edit',
    method: 'post',
    data
  })
}

// 删除画板管理配置
export function deleteDrawingBoard(ids: string[]) {
  return request({
    url: '/api/base/drawing/management/deleteIds',
    method: 'delete',
    data: ids
  })
} 