package org.thingsboard.server.dao.sql.deviceIcon;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.deviceIcon.DeviceIcon;
import org.thingsboard.server.dao.util.imodel.query.deviceIcon.DeviceIconPageRequest;

import java.util.List;

/**
 * 设备图标Mapper接口
 */
@Mapper
public interface DeviceIconMapper extends BaseMapper<DeviceIcon> {
    
    /**
     * 分页查询设备图标
     */
    IPage<DeviceIcon> findByPage(DeviceIconPageRequest request);
    
    /**
     * 根据设备类型查询设备图标
     */
    List<DeviceIcon> findByDeviceType(@Param("deviceType") String deviceType, @Param("tenantId") String tenantId);
    
    /**
     * 根据设备类型和设备状态查询设备图标
     */
    DeviceIcon findByDeviceTypeAndStatus(@Param("deviceType") String deviceType, @Param("deviceStatus") String deviceStatus, @Param("tenantId") String tenantId);
    
    /**
     * 批量插入设备图标
     */
    void batchInsert(@Param("list") List<DeviceIcon> list);
    
    /**
     * 根据设备类型删除设备图标
     */
    void deleteByDeviceType(@Param("deviceType") String deviceType, @Param("tenantId") String tenantId);
}
