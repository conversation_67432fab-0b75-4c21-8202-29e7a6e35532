<!-- 事件总览表单 -->
<template>
  <el-dialog
    v-model="visible"
    :title="props.isView ? '查看事件' : (props.isEdit ? '编辑事件' : '新增事件')"
    width="1000px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      class="event-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="事件类型" prop="typeCategory">
            <el-cascader
              v-model="formData.typeCategory"
              :options="typeCascaderOptions"
              :props="cascaderProps"
              placeholder="请选择事件类型"
              style="width: 100%"
              :disabled="props.isView"
              @change="handleTypeCategoryChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="事件名称" prop="title">
            <el-input
              v-model="formData.title"
              placeholder="请输入事件名称"
              :disabled="props.isView"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="创建时间" prop="createTime">
            <el-date-picker
              v-model="formData.createTime"
              type="datetime"
              placeholder="请选择创建时间"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              :disabled="props.isView"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="地址" prop="address">
            <el-input
              v-model="formData.address"
              placeholder="请输入地址"
              :disabled="props.isView"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="formData.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
              :disabled="props.isView"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 驳回原因显示（仅在查看已驳回事件时显示） -->
      <el-row v-if="props.isView && props.editData?.status === 'REJECTED' && props.editData?.rejectReason" :gutter="20">
        <el-col :span="24">
          <el-form-item label="驳回原因">
            <el-input
              :value="props.editData.rejectReason"
              type="textarea"
              :rows="3"
              readonly
              style="background-color: #f5f7fa;"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 地图选择区域 -->
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item :label="props.isView ? '事件位置' : '选择位置'">
            <div class="map-selector">
              <div class="map-selector-header" v-if="!props.isView">
                <span>请在地图上点击选择事件发生位置</span>
              </div>
              <div class="map-container">
                <FormMap
                  v-model="mapLocation"
                  :show-input="true"
                  :handle-inverse-geocodeing="handleInverseGeocodeing"
                  style="height: 350px"
                  @change="handleMapLocationChange"
                />
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">{{ props.isView ? '关闭' : '取消' }}</el-button>
        <el-button
          v-if="!props.isView"
          type="primary"
          @click="handleSubmit"
          :loading="submitLoading"
        >
          {{ props.isEdit ? '更新' : '创建' }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import FormMap from '@/components/arcMap/FormMap.vue'
import { CreateEventOverview, UpdateEventOverview } from '@/api/workorder/eventOverview'
import request from '@/plugins/axios'

interface EventFormData {
  id?: string
  typeCategory: string[]
  type: string
  typeName?: string
  title: string
  createTime: string
  address: string
  remark: string
  coordinate?: string
  status?: string
  rejectReason?: string
}

const props = defineProps<{
  modelValue: boolean
  isEdit?: boolean
  isView?: boolean
  editData?: EventFormData
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'submit': [data: EventFormData]
}>()

const visible = ref(false)
const formRef = ref()
const submitLoading = ref(false)
const typeCascaderOptions = ref<any[]>([]) // 级联选择器选项
const allTypeData = ref<any[]>([]) // 存储完整的类型数据

// 级联选择器配置
const cascaderProps = {
  value: 'id',
  label: 'name',
  children: 'children',
  emitPath: true, // 返回完整路径用于反显
  checkStrictly: false // 只能选择叶子节点
}

// 地图相关
const mapLocation = ref<number[]>([95.787329, 40.516879]) // 使用全局配置的默认中心点

// 表单数据
const formData = reactive<EventFormData>({
  typeCategory: [],
  type: '',


  typeName: '',
  title: '',
  createTime: '',
  address: '',
  remark: ''
})

// 表单验证规则
const formRules = {
  typeCategory: [{ required: true, message: '请选择事件类型', trigger: 'change' }],
  title: [{ required: true, message: '请输入事件名称', trigger: 'blur' }],
  createTime: [{ required: true, message: '请选择创建时间', trigger: 'change' }],
  address: [{ required: true, message: '请输入地址', trigger: 'blur' }]
}

// 监听弹窗显示状态
watch(() => props.modelValue, async (val) => {
  visible.value = val
  if (val) {
    await loadTypeOptions()
    initForm()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 初始化表单
const initForm = () => {
  if ((props.isEdit || props.isView) && props.editData) {
    // 编辑模式或查看模式，加载数据
    Object.assign(formData, props.editData)
    if (props.editData.coordinate) {
      const coords = props.editData.coordinate.split(',')
      mapLocation.value = [parseFloat(coords[0]), parseFloat(coords[1])]
    }

    // 根据type找到对应的级联路径（如果类型数据已加载）
    if (allTypeData.value.length > 0) {
      setTypeCategoryFromType(props.editData.type)
    }
  } else {
    // 新增时设置默认值
    const now = new Date()
    // 转换为东八区时间
    const chinaTime = new Date(now.getTime() + (8 * 60 * 60 * 1000))
    const formatTime = chinaTime.toISOString().slice(0, 19).replace('T', ' ')

    Object.assign(formData, {
      typeCategory: [],
      type: '',
      typeName: '',
      title: '',
      createTime: formatTime,
      address: '',
      remark: ''
    })
    mapLocation.value = [95.787329, 40.516879] // 重置为默认坐标
  }
}

// 根据type设置级联路径
const setTypeCategoryFromType = (type: string) => {
  if (!type || allTypeData.value.length === 0) {
    return
  }

  // 在所有数据中查找当前type对应的父级
  for (const parentType of allTypeData.value) {
    if (parentType.children) {
      const childType = parentType.children.find((child: any) => child.id === type)
      if (childType) {
        formData.typeCategory = [parentType.id, childType.id] // 设置完整路径用于反显
        formData.typeName = `${parentType.name} / ${childType.name}`
        break
      }
    }
  }
}

// 加载事件类型选项
const loadTypeOptions = async () => {
  try {
    const res = await request({
      url: '/api/workOrderType/list',
      method: 'get',
      params: { status: 1 }
    })

    if (res.data?.data) {
      // 存储完整的类型数据
      allTypeData.value = res.data.data

      // 只获取父级类型（parentId为"0"的是父级类型）
      const parentTypes = res.data.data.filter((item: any) =>
        item.parentId === "0" || item.parentId === 0 || !item.parentId
      )

      // 构建级联选择器的数据结构
      typeCascaderOptions.value = parentTypes.map((parent: any) => ({
        id: parent.id,
        name: parent.name,
        children: parent.children || []
      }))

      // 如果是编辑模式或查看模式且有type数据，设置级联路径
      if ((props.isEdit || props.isView) && props.editData?.type) {
        setTypeCategoryFromType(props.editData.type)
      }
    }
  } catch (error) {
    console.error('获取事件类型失败:', error)
    ElMessage.error('获取事件类型失败')
  }
}

// 处理级联选择器变化
const handleTypeCategoryChange = (value: any) => {
  if (value && Array.isArray(value) && value.length >= 2) {
    // value是数组 [父级ID, 子级ID]，只取子级ID作为type
    const childTypeId = value[value.length - 1]
    formData.type = childTypeId

    // 查找对应的类型名称
    const parentTypeId = value[0]
    const parentType = allTypeData.value.find(item => item.id === parentTypeId)
    if (parentType && parentType.children) {
      const childType = parentType.children.find((child: any) => child.id === childTypeId)
      if (childType) {
        formData.typeName = `${parentType.name} / ${childType.name}`
      }
    }
  } else {
    formData.type = ''
    formData.typeName = ''
  }
}



// FormMap组件的逆地理编码处理
const handleInverseGeocodeing = (res: any) => {
  if (res.data?.result?.formatted_address) {
    formData.address = res.data.result.formatted_address
  }
}

// 地图位置变化处理
const handleMapLocationChange = (location: number[]) => {
  if (location && location.length === 2) {
    formData.coordinate = `${location[0]},${location[1]}`
    ElMessage.success('位置选择成功')
  }
}

// 提交表单
const handleSubmit = async () => {
  // 查看模式下不执行提交
  if (props.isView) return

  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitLoading.value = true

    // 构建提交数据
    const submitData = {
      title: formData.title,
      type: formData.type,
      typeName: formData.typeName,
      address: formData.address,
      remark: formData.remark,
      coordinate: formData.coordinate,
      coordinateName: formData.address
    }

    if (props.isEdit && formData.id) {
      // 编辑模式
      await UpdateEventOverview({
        id: formData.id,
        ...submitData
      })
      ElMessage.success('事件更新成功')
    } else {
      // 新增模式
      await CreateEventOverview(submitData)
      ElMessage.success('事件创建成功')
    }

    visible.value = false
    emit('submit', formData)

  } catch (error) {
    ElMessage.error(props.isEdit ? '事件更新失败' : '事件创建失败')
  } finally {
    submitLoading.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  formRef.value?.resetFields()
  mapLocation.value = [95.787329, 40.516879] // 重置为默认坐标
}
</script>

<style lang="scss" scoped>
.event-form {
  .address-input-group {
    display: flex;
    align-items: center;
  }

  .map-selector {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;
    width: 100%;

    .map-selector-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background-color: #f5f7fa;
      border-bottom: 1px solid #dcdfe6;

      span {
        font-size: 14px;
        color: #606266;
      }
    }

    .map-container {
      height: 350px;
      width: 100%;

      :deep(.location-map) {
        width: 100%;
        height: 100%;
      }

      :deep(.locate-search-box) {
        width: 100%;
      }
    }

    .coordinate-info {
      padding: 8px 16px;
      background-color: #f0f9ff;
      border-top: 1px solid #dcdfe6;
      font-size: 12px;
      color: #409eff;
    }
  }
}

// 确保表单项的宽度一致
:deep(.el-form-item__content) {
  width: 100%;
}

:deep(.el-input) {
  width: 100%;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-date-editor) {
  width: 100%;
}
</style>
