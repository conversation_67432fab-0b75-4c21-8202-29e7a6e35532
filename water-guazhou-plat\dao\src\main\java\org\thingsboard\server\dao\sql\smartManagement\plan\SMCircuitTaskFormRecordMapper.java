package org.thingsboard.server.dao.sql.smartManagement.plan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitTaskFormRecord;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.SMCircuitTaskFormRecordPageRequest;

import java.util.List;

/**
 * 巡检任务表单记录Mapper接口
 */
@Mapper
public interface SMCircuitTaskFormRecordMapper extends BaseMapper<SMCircuitTaskFormRecord> {

    /**
     * 分页查询表单记录
     *
     * @param request 分页查询请求
     * @return 分页结果
     */
    IPage<SMCircuitTaskFormRecord> findByPage(SMCircuitTaskFormRecordPageRequest request);

    /**
     * 根据任务ID查询表单记录
     *
     * @param taskId 任务ID
     * @return 表单记录列表
     */
    List<SMCircuitTaskFormRecord> findByTaskId(@Param("taskId") String taskId);

    /**
     * 根据任务编号查询表单记录
     *
     * @param taskCode 任务编号
     * @return 表单记录列表
     */
    List<SMCircuitTaskFormRecord> findByTaskCode(@Param("taskCode") String taskCode);

    /**
     * 更新实体
     *
     * @param entity 实体信息
     * @return 是否更新成功
     */
    boolean update(SMCircuitTaskFormRecord entity);



    /**
     * 更新检验结果
     *
     * @param id 记录ID
     * @param result 检验结果
     * @param resultDescription 结果描述
     * @param attachments 附件
     * @param checkUserId 检验人员ID
     * @return 更新数量
     */
    int updateCheckResult(@Param("id") String id, 
                         @Param("result") String result,
                         @Param("resultDescription") String resultDescription,
                         @Param("attachments") String attachments,
                         @Param("checkUserId") String checkUserId);

    /**
     * 根据任务ID删除表单记录
     *
     * @param taskId 任务ID
     * @return 删除数量
     */
    int deleteByTaskId(@Param("taskId") String taskId);
}
