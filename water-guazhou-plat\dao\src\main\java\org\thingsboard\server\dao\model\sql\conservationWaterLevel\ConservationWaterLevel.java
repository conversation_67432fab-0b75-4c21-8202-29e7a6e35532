package org.thingsboard.server.dao.model.sql.conservationWaterLevel;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 涵养水位数据
 */
@Data
@TableName("tb_conservation_water_level")
@ApiModel(value = "涵养水位数据", description = "地下水涵养水位监测和分析数据")
public class ConservationWaterLevel {

    @TableId
    @ApiModelProperty(value = "主键ID")
    private String id;
    
    @ApiModelProperty(value = "租户ID")
    private String tenantId;
    
    @ApiModelProperty(value = "测点ID")
    private String stationId;
    
    @ApiModelProperty(value = "测点名称")
    @TableField(exist = false)
    private String stationName;

    @ApiModelProperty(value = "测点位置")
    @TableField(exist = false)
    private String stationLocation;

    @ApiModelProperty(value = "原水液位(米)")
    private BigDecimal rawWaterLevel;
    
    @ApiModelProperty(value = "地下水位(米)")
    private BigDecimal groundwaterLevel;
    
    @ApiModelProperty(value = "液位变化量(米)")
    private BigDecimal levelChange;
    
    @ApiModelProperty(value = "降雨量(毫米)")
    private BigDecimal rainfallAmount;
    
    @ApiModelProperty(value = "蒸发量(毫米)")
    private BigDecimal evaporationAmount;
    
    @ApiModelProperty(value = "地表径流量(立方米)")
    private BigDecimal surfaceRunoff;
    
    @ApiModelProperty(value = "地下水开采量(立方米)")
    private BigDecimal extractionAmount;
    
    @ApiModelProperty(value = "土壤含水率(%)")
    private BigDecimal soilMoisture;
    
    @ApiModelProperty(value = "渗透系数")
    private BigDecimal permeabilityCoefficient;
    
    @ApiModelProperty(value = "记录时间")
    private Date recordTime;
    
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    
    @ApiModelProperty(value = "备注")
    private String remark;
    
    @ApiModelProperty(value = "数据来源 (1-手动录入, 2-设备采集)")
    private String dataSource;
    
    @ApiModelProperty(value = "创建人")
    private String creator;
    
    @ApiModelProperty(value = "创建人姓名")
    @TableField(exist = false)
    private String creatorName;
}
