package org.thingsboard.server.dao.mapper.workOrder;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.thingsboard.server.dao.model.sql.workOrder.EventOverview;

import java.util.List;
import java.util.Map;

/**
 * 事件总览Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface EventOverviewMapper extends BaseMapper<EventOverview> {

    /**
     * 获取事件统计信息
     * 
     * @param tenantId 租户ID
     * @param projectId 项目ID（可选）
     * @return 统计信息
     */
    @Select({
        "<script>",
        "SELECT ",
        "  COUNT(*) as total_count,",
        "  COUNT(CASE WHEN status IN ('PENDING', 'ASSIGN') THEN 1 END) as pending_count,",
        "  COUNT(CASE WHEN status IN ('RESOLVING', 'ARRIVING', 'PROCESSING') THEN 1 END) as processing_count,",
        "  COUNT(CASE WHEN status IN ('COMPLETE', 'APPROVED') THEN 1 END) as completed_count,",
        "  COUNT(CASE WHEN status IN ('TERMINATED', 'REJECTED') THEN 1 END) as terminated_count,",
        "  COUNT(CASE WHEN DATE(create_time) = CURRENT_DATE THEN 1 END) as today_new,",
        "  COUNT(CASE WHEN create_time >= DATE_TRUNC('week', CURRENT_DATE) THEN 1 END) as week_new,",
        "  COUNT(CASE WHEN create_time >= DATE_TRUNC('month', CURRENT_DATE) THEN 1 END) as month_new,",
        "  COUNT(CASE WHEN coordinate IS NOT NULL AND coordinate != '' THEN 1 END) as events_with_location,",
        "FROM event_overview ",
        "WHERE tenant_id = #{tenantId}",
        "<if test='projectId != null and projectId != \"\"'>",
        "  AND project_id = #{projectId}",
        "</if>",
        "</script>"
    })
    Map<String, Object> getEventStatistics(@Param("tenantId") String tenantId, @Param("projectId") String projectId);

    /**
     * 获取事件热点地区统计
     * 
     * @param tenantId 租户ID
     * @param limit 返回数量限制
     * @return 热点地区列表
     */
    @Select({
        "SELECT ",
        "  SUBSTRING(address, 1, 20) as area_name,",
        "  COUNT(*) as event_count,",
        "  COUNT(CASE WHEN status IN ('PENDING', 'ASSIGN') THEN 1 END) as pending_count",
        "FROM event_overview ",
        "WHERE tenant_id = #{tenantId} AND address IS NOT NULL AND address != '' ",
        "GROUP BY SUBSTRING(address, 1, 20) ",
        "HAVING COUNT(*) >= 2 ",
        "ORDER BY event_count DESC ",
        "LIMIT #{limit}"
    })
    List<Map<String, Object>> getEventHotspotStatistics(@Param("tenantId") String tenantId, @Param("limit") int limit);

    /**
     * 获取事件趋势统计（按天）
     * 
     * @param tenantId 租户ID
     * @param days 统计天数
     * @return 趋势数据
     */
    @Select({
        "SELECT ",
        "  DATE(create_time) as date,",
        "  COUNT(*) as event_count,",
        "  COUNT(CASE WHEN status IN ('PENDING', 'ASSIGN') THEN 1 END) as pending_count,",
        "  COUNT(CASE WHEN status IN ('COMPLETE', 'APPROVED') THEN 1 END) as completed_count",
        "FROM event_overview ",
        "WHERE tenant_id = #{tenantId} ",
        "  AND create_time >= CURRENT_DATE - INTERVAL '#{days} days' ",
        "GROUP BY DATE(create_time) ",
        "ORDER BY date DESC"
    })
    List<Map<String, Object>> getEventTrendStatistics(@Param("tenantId") String tenantId, @Param("days") int days);

    /**
     * 获取事件状态分布统计
     * 
     * @param tenantId 租户ID
     * @param projectId 项目ID（可选）
     * @return 状态分布数据
     */
    @Select({
        "<script>",
        "SELECT ",
        "  status,",
        "  COUNT(*) as count,",
        "  ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage",
        "FROM event_overview ",
        "WHERE tenant_id = #{tenantId}",
        "<if test='projectId != null and projectId != \"\"'>",
        "  AND project_id = #{projectId}",
        "</if>",
        "GROUP BY status ",
        "ORDER BY count DESC",
        "</script>"
    })
    List<Map<String, Object>> getEventStatusDistribution(@Param("tenantId") String tenantId, @Param("projectId") String projectId);

    /**
     * 获取处理效率统计
     *
     * @param tenantId 租户ID
     * @param days 统计天数
     * @return 效率统计数据
     */
    @Select({
        "SELECT ",
        "  COUNT(*) as total_count,",
        "  COUNT(CASE WHEN status IN ('COMPLETE', 'APPROVED') THEN 1 END) as completed_count,",
        "  COUNT(CASE WHEN status IN ('PENDING', 'ASSIGN') THEN 1 END) as pending_count",
        "FROM event_overview ",
        "WHERE tenant_id = #{tenantId} ",
        "  AND create_time >= CURRENT_DATE - INTERVAL '#{days} days'"
    })
    Map<String, Object> getProcessEfficiencyStatistics(@Param("tenantId") String tenantId, @Param("days") int days);

    /**
     * 获取处理时长统计
     *
     * @param tenantId 租户ID
     * @param days 统计天数
     * @return 处理时长统计
     */
    @Select({
        "SELECT ",
        "  status as duration_range,",
        "  COUNT(*) as count",
        "FROM event_overview ",
        "WHERE tenant_id = #{tenantId} ",
        "  AND create_time >= CURRENT_DATE - INTERVAL '#{days} days' ",
        "GROUP BY status ",
        "ORDER BY count DESC"
    })
    Map<String, Object> getProcessDurationStatistics(@Param("tenantId") String tenantId, @Param("days") int days);

    /**
     * 获取超时事件列表
     *
     * @param tenantId 租户ID
     * @param limit 返回数量限制
     * @return 超时事件列表
     */
    @Select({
        "SELECT * FROM event_overview ",
        "WHERE tenant_id = #{tenantId} ",
        "  AND status NOT IN ('COMPLETE', 'APPROVED', 'TERMINATED', 'REJECTED') ",
        "ORDER BY create_time ASC ",
        "LIMIT #{limit}"
    })
    List<EventOverview> getOverdueEvents(@Param("tenantId") String tenantId, @Param("limit") int limit);

    /**
     * 获取高优先级事件列表
     *
     * @param tenantId 租户ID
     * @param limit 返回数量限制
     * @return 高优先级事件列表
     */
    @Select({
        "SELECT * FROM event_overview ",
        "WHERE tenant_id = #{tenantId} ",
        "  AND level IN ('高', 'HIGH') ",
        "  AND status IN ('PENDING', 'ASSIGN', 'RESOLVING', 'PROCESSING') ",
        "ORDER BY create_time DESC ",
        "LIMIT #{limit}"
    })
    List<EventOverview> getHighPriorityEvents(@Param("tenantId") String tenantId, @Param("limit") int limit);
}
