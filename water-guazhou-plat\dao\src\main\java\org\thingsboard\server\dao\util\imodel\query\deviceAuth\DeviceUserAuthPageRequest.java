package org.thingsboard.server.dao.util.imodel.query.deviceAuth;

import lombok.Data;
import org.thingsboard.server.dao.model.sql.deviceAuth.DeviceUserAuth;
import org.thingsboard.server.dao.util.imodel.query.PageableQueryEntity;

/**
 * 设备用户权限关联分页查询请求
 */
@Data
public class DeviceUserAuthPageRequest extends PageableQueryEntity<DeviceUserAuth> {
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 设备名称
     */
    private String deviceName;
    
    /**
     * 设备编码
     */
    private String deviceSerial;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 用户名称
     */
    private String userName;
    
    /**
     * 权限类型
     */
    private Integer authType;
    
    /**
     * 租户ID
     */
    private String tenantId;
}
