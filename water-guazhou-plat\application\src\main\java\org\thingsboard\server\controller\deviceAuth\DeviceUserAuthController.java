package org.thingsboard.server.controller.deviceAuth;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.deviceAuth.DeviceUserAuthService;
import org.thingsboard.server.dao.model.sql.deviceAuth.DeviceUserAuth;
import org.thingsboard.server.dao.util.imodel.query.deviceAuth.DeviceUserAuthBatchSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.deviceAuth.DeviceUserAuthPageRequest;
import org.thingsboard.server.dao.util.imodel.query.deviceAuth.DeviceUserAuthSaveRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 设备用户权限关联控制器
 */
@Api(tags = "设备用户权限管理")
@RestController
@RequestMapping("/api/deviceAuth")
public class DeviceUserAuthController extends BaseController {

    @Autowired
    private DeviceUserAuthService deviceUserAuthService;

    @ApiOperation(value = "分页查询设备用户权限关联")
    @GetMapping
    public IstarResponse findByPage(DeviceUserAuthPageRequest request) throws ThingsboardException {
        request.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        return IstarResponse.ok(deviceUserAuthService.findByPage(request));
    }

    @ApiOperation(value = "根据设备ID查询设备用户权限关联")
    @GetMapping("/device/{deviceId}")
    public IstarResponse findByDeviceId(@PathVariable String deviceId) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(deviceUserAuthService.findByDeviceId(deviceId, tenantId));
    }

    @ApiOperation(value = "根据用户ID查询设备用户权限关联")
    @GetMapping("/user/{userId}")
    public IstarResponse findByUserId(@PathVariable String userId) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(deviceUserAuthService.findByUserId(userId, tenantId));
    }

    @ApiOperation(value = "保存设备用户权限关联")
    @PostMapping
    public IstarResponse save(@RequestBody DeviceUserAuthSaveRequest request) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(deviceUserAuthService.save(request, tenantId));
    }

    @ApiOperation(value = "批量保存设备用户权限关联")
    @PostMapping("/batch")
    public IstarResponse batchSave(@RequestBody DeviceUserAuthBatchSaveRequest request) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        deviceUserAuthService.batchSave(request, tenantId);
        return IstarResponse.ok();
    }

    @ApiOperation(value = "删除设备用户权限关联")
    @DeleteMapping("/{id}")
    public IstarResponse delete(@PathVariable String id) {
        return IstarResponse.ok(deviceUserAuthService.delete(id));
    }

    @ApiOperation(value = "根据设备ID删除设备用户权限关联")
    @DeleteMapping("/device/{deviceId}")
    public IstarResponse deleteByDeviceId(@PathVariable String deviceId) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        deviceUserAuthService.deleteByDeviceId(deviceId, tenantId);
        return IstarResponse.ok();
    }

    @ApiOperation(value = "检查用户是否有设备权限")
    @GetMapping("/check")
    public IstarResponse checkUserDeviceAuth(@RequestParam String deviceId, @RequestParam String userId) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        List<DeviceUserAuth> authList = deviceUserAuthService.findByDeviceId(deviceId, tenantId);
        boolean hasAuth = false;
        Integer authType = null;

        for (DeviceUserAuth auth : authList) {
            if (auth.getUserId().equals(userId)) {
                hasAuth = true;
                authType = auth.getAuthType();
                break;
            }
        }

        return IstarResponse.ok(new CheckAuthResult(hasAuth, authType));
    }

    /**
     * 权限检查结果
     */
    private static class CheckAuthResult {
        private boolean hasAuth;
        private Integer authType;

        public CheckAuthResult(boolean hasAuth, Integer authType) {
            this.hasAuth = hasAuth;
            this.authType = authType;
        }

        public boolean isHasAuth() {
            return hasAuth;
        }

        public void setHasAuth(boolean hasAuth) {
            this.hasAuth = hasAuth;
        }

        public Integer getAuthType() {
            return authType;
        }

        public void setAuthType(Integer authType) {
            this.authType = authType;
        }
    }
}
