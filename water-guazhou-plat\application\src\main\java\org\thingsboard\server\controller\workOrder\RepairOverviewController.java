package org.thingsboard.server.controller.workOrder;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.workOrder.EventOverview;
import org.thingsboard.server.dao.orderWork.RepairOverviewService;
import org.thingsboard.server.dao.util.imodel.query.workOrder.RepairOverviewPageRequest;
import org.thingsboard.server.dao.util.imodel.response.model.IModel;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

import java.util.List;
import java.util.Map;

/**
 * 维修总览控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/repairOverview")
@IStarController
public class RepairOverviewController extends BaseController {

    @Autowired
    private RepairOverviewService repairOverviewService;

    /**
     * 分页查询维修记录
     *
     * @param model 模型
     * @param request 查询请求
     * @return 分页结果
     * @throws ThingsboardException 异常
     */
    @GetMapping
    public IPage<EventOverview> getRepairOverviewPage(IModel model, RepairOverviewPageRequest request) throws ThingsboardException {
        try {
            // 设置租户ID
            request.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));

            return repairOverviewService.findRepairOverviewByPage(request);
        } catch (Exception e) {
            log.error("Failed to get repair overview page", e);
            throw handleException(e);
        }
    }

    /**
     * 获取维修总览统计数据
     *
     * @param model 模型
     * @param request 查询请求
     * @return 统计数据
     * @throws ThingsboardException 异常
     */
    @GetMapping("/statistics")
    public Map<String, Object> getStatistics(IModel model, RepairOverviewPageRequest request) throws ThingsboardException {
        try {
            // 设置租户ID
            request.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));

            return repairOverviewService.getRepairStatistics(request);
        } catch (Exception e) {
            log.error("Failed to get repair statistics", e);
            throw handleException(e);
        }
    }

    /**
     * 导出维修记录
     *
     * @param model 模型
     * @param request 查询条件
     * @return 维修记录列表
     * @throws ThingsboardException 异常
     */
    @GetMapping("/export")
    public List<EventOverview> exportRepairOverview(IModel model, RepairOverviewPageRequest request) throws ThingsboardException {
        try {
            // 设置租户ID
            request.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));

            return repairOverviewService.exportRepairOverview(request);
        } catch (Exception e) {
            log.error("Failed to export repair overview", e);
            throw handleException(e);
        }
    }
}
