package org.thingsboard.server.controller.workOrder;

import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.orderWork.RepairOverviewService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;


import java.util.List;
import java.util.Map;

/**
 * 维修总览控制器
 */
@RestController
@RequestMapping("/api/repairOverview")
public class RepairOverviewController extends BaseController {

    @Autowired
    private RepairOverviewService repairOverviewService;

    /**
     * 获取维修总览统计数据
     */
    @GetMapping("/statistics")
    public IstarResponse getStatistics(
            @RequestParam(required = false) String createStartTime,
            @RequestParam(required = false) String createEndTime,
            @RequestParam(required = false) String expireStartTime,
            @RequestParam(required = false) String expireEndTime,
            @RequestParam(required = false) String status
    ) throws ThingsboardException {
        JSONObject params = new JSONObject();
        params.put("createStartTime", createStartTime);
        params.put("createEndTime", createEndTime);
        params.put("expireStartTime", expireStartTime);
        params.put("expireEndTime", expireEndTime);
        params.put("status", status);
        params.put("tenantId", getTenantId().toString());

        Map<String, Object> result = repairOverviewService.getStatistics(params);
        return IstarResponse.ok(result);
    }

    /**
     * 获取维修记录分页列表
     */
    @GetMapping("/records")
    public IstarResponse getRecords(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String createStartTime,
            @RequestParam(required = false) String createEndTime,
            @RequestParam(required = false) String expireStartTime,
            @RequestParam(required = false) String expireEndTime,
            @RequestParam(required = false) String status
    ) throws ThingsboardException {
        JSONObject params = new JSONObject();
        params.put("page", page);
        params.put("size", size);
        params.put("createStartTime", createStartTime);
        params.put("createEndTime", createEndTime);
        params.put("expireStartTime", expireStartTime);
        params.put("expireEndTime", expireEndTime);
        params.put("status", status);
        params.put("tenantId", getTenantId().toString());

        Map<String, Object> result = repairOverviewService.getRecords(params);
        return IstarResponse.ok(result);
    }

    /**
     * 导出维修总览数据
     */
    @PostMapping("/export")
    public IstarResponse exportData(@RequestBody JSONObject params) throws ThingsboardException {
        params.put("tenantId", getTenantId().toString());
        String result = repairOverviewService.exportData(params);
        return IstarResponse.ok(result);
    }

    /**
     * 获取维修类型选项
     */
    @GetMapping("/types")
    public IstarResponse getRepairTypes() throws ThingsboardException {
        List<Map<String, Object>> result = repairOverviewService.getRepairTypes(getTenantId().toString());
        return IstarResponse.ok(result);
    }

    /**
     * 获取维修状态选项
     */
    @GetMapping("/statuses")
    public IstarResponse getRepairStatuses() throws ThingsboardException {
        List<Map<String, Object>> result = repairOverviewService.getRepairStatuses();
        return IstarResponse.ok(result);
    }
}
