import { request } from '@/plugins/axios'

/**
 * 维修总览统计数据接口
 */
export interface RepairOverviewStatistics {
  /** 月度统计数据 */
  monthlyData: Array<{
    month: string
    waterSupplyIssues: number  // 水表问题
    heatSupplyIssues: number   // 热表设备
    pipelineMaintenance: number // 管网维修
    valveMaintenance: number   // 阀门维修
  }>
  /** 维修记录列表 */
  repairRecords: Array<{
    id: string
    deviceName: string        // 维修部件
    location: string         // 上报地址
    repairType: string       // 维修类型
    repairStatus: string     // 维修状态
    createTime: string       // 创建时间
    creator: string          // 创建人
    duration: number         // 耗时(小时)
  }>
  /** 统计汇总 */
  summary: {
    waterSupplyTotal: number   // 水表问题总数
    heatSupplyTotal: number    // 热表设备总数
    pipelineTotal: number      // 管网维修总数
    valveTotal: number         // 阀门维修总数
  }
}

/**
 * 维修总览查询参数
 */
export interface RepairOverviewRequest {
  /** 创建时间开始（时间戳） */
  createFromTime?: number
  /** 创建时间结束（时间戳） */
  createToTime?: number
  /** 到期时间开始（时间戳） */
  expireFromTime?: number
  /** 到期时间结束（时间戳） */
  expireToTime?: number
  /** 事件状态 */
  status?: string
  /** 分页参数 */
  page?: number
  size?: number
  /** 排序字段 */
  orderBy?: string
  /** 排序方向 */
  orderDirection?: string
}

/**
 * 分页查询维修记录
 */
export const GetRepairOverviewPage = (params: RepairOverviewRequest) => {
  return request({
    url: '/api/repairOverview',
    method: 'get',
    params
  })
}

/**
 * 获取维修总览统计数据
 */
export const GetRepairOverviewStatistics = (params?: RepairOverviewRequest) => {
  return request({
    url: '/api/repairOverview/statistics',
    method: 'get',
    params
  })
}

/**
 * 导出维修记录
 */
export const ExportRepairOverview = (params?: RepairOverviewRequest) => {
  return request({
    url: '/api/repairOverview/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
