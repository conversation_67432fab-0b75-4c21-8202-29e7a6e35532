import { request } from '@/plugins/axios'

/**
 * 维修总览统计数据接口
 */
export interface RepairOverviewStatistics {
  /** 月度统计数据 */
  monthlyData: Array<{
    month: string
    waterSupplyIssues: number  // 水表问题
    heatSupplyIssues: number   // 热表设备
    pipelineMaintenance: number // 管网维修
    valveMaintenance: number   // 阀门维修
  }>
  /** 维修记录列表 */
  repairRecords: Array<{
    id: string
    deviceName: string        // 维修部件
    location: string         // 上报地址
    repairType: string       // 维修类型
    repairStatus: string     // 维修状态
    createTime: string       // 创建时间
    creator: string          // 创建人
    duration: number         // 耗时(小时)
  }>
  /** 统计汇总 */
  summary: {
    waterSupplyTotal: number   // 水表问题总数
    heatSupplyTotal: number    // 热表设备总数
    pipelineTotal: number      // 管网维修总数
    valveTotal: number         // 阀门维修总数
  }
}

/**
 * 维修总览查询参数
 */
export interface RepairOverviewRequest {
  /** 查询年份 */
  year?: string
  /** 开始时间 */
  startTime?: string
  /** 结束时间 */
  endTime?: string
  /** 维修类型 */
  repairType?: string
  /** 维修状态 */
  repairStatus?: string
  /** 分页参数 */
  page?: number
  size?: number
}

/**
 * 获取维修总览统计数据
 */
export const GetRepairOverviewStatistics = (params?: RepairOverviewRequest) => {
  return request({
    url: '/api/repairOverview/statistics',
    method: 'get',
    params
  })
}

/**
 * 获取维修记录分页列表
 */
export const GetRepairRecordPage = (params: RepairOverviewRequest) => {
  return request({
    url: '/api/repairOverview/records',
    method: 'get',
    params
  })
}

/**
 * 导出维修总览数据
 */
export const ExportRepairOverview = (params?: RepairOverviewRequest) => {
  return request({
    url: '/api/repairOverview/export',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}

/**
 * 获取维修类型选项
 */
export const GetRepairTypeOptions = () => {
  return request({
    url: '/api/repairOverview/types',
    method: 'get'
  })
}

/**
 * 获取维修状态选项
 */
export const GetRepairStatusOptions = () => {
  return request({
    url: '/api/repairOverview/statuses',
    method: 'get'
  })
}
