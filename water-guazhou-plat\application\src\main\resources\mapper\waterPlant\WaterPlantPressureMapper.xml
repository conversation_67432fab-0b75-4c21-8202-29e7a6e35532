<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.waterPlant.WaterPlantPressureMapper">
    
    <select id="findByPage" resultType="org.thingsboard.server.dao.model.sql.waterPlant.WaterPlantPressure">
        SELECT 
            a.*
        FROM tb_water_plant_pressure a
        <where>
            <if test="tenantId != null and tenantId != ''">
                AND a.tenant_id = #{tenantId}
            </if>
            <if test="waterPlantId != null and waterPlantId != ''">
                AND a.water_plant_id = #{waterPlantId}
            </if>
            <if test="waterPlantName != null and waterPlantName != ''">
                AND a.water_plant_name LIKE CONCAT('%', #{waterPlantName}, '%')
            </if>
            <if test="startTime != null">
                AND a.record_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND a.record_time &lt;= #{endTime}
            </if>
            <if test="dataSource != null and dataSource != ''">
                AND a.data_source = #{dataSource}
            </if>
        </where>
        ORDER BY a.record_time DESC
    </select>
    
    <select id="checkExists" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM tb_water_plant_pressure
        WHERE water_plant_id = #{waterPlantId}
        AND DATE(record_time) = DATE(#{recordTime})
        AND tenant_id = #{tenantId}
    </select>
    
    <select id="exportList" resultType="org.thingsboard.server.dao.model.sql.waterPlant.WaterPlantPressure">
        SELECT 
            a.*
        FROM tb_water_plant_pressure a
        <where>
            <if test="tenantId != null and tenantId != ''">
                AND a.tenant_id = #{tenantId}
            </if>
            <if test="waterPlantId != null and waterPlantId != ''">
                AND a.water_plant_id = #{waterPlantId}
            </if>
            <if test="waterPlantName != null and waterPlantName != ''">
                AND a.water_plant_name LIKE CONCAT('%', #{waterPlantName}, '%')
            </if>
            <if test="startTime != null">
                AND a.record_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND a.record_time &lt;= #{endTime}
            </if>
            <if test="dataSource != null and dataSource != ''">
                AND a.data_source = #{dataSource}
            </if>
        </where>
        ORDER BY a.record_time DESC
    </select>
    
    <insert id="batchInsert">
        INSERT INTO tb_water_plant_pressure (
            id, water_plant_id, water_plant_name, model, pressure, 
            data_source, record_time, remark, creator, create_time, tenant_id
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.waterPlantId}, #{item.waterPlantName}, #{item.model}, #{item.pressure},
                #{item.dataSource}, #{item.recordTime}, #{item.remark}, #{item.creator}, #{item.createTime}, #{item.tenantId}
            )
        </foreach>
    </insert>
</mapper>
