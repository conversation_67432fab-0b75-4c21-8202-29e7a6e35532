package org.thingsboard.server.dao.util.imodel.query.waterPlant;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 水厂压力信息保存请求
 */
@Data
public class WaterPlantPressureSaveRequest {
    /**
     * ID
     */
    private String id;

    /**
     * 水厂ID
     */
    private String waterPlantId;

    /**
     * 水厂名称
     */
    private String waterPlantName;

    /**
     * 设备编号
     */
    private String deviceSerial;

    /**
     * 出厂水压(MPa)
     */
    private BigDecimal pressure;

    /**
     * 记录时间
     */
    private Date recordTime;

    /**
     * 备注
     */
    private String remark;
}
