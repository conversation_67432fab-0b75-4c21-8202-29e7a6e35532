package org.thingsboard.server.controller.base;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.accessControl.AccessControlVideoService;
import org.thingsboard.server.dao.model.sql.AccessControlVideo;
import org.thingsboard.server.dao.model.sql.VideoEntity;

import java.util.List;

/**
 * 门禁视频关联管理控制器
 * 
 * <AUTHOR>
 * @date 2024
 */
@RestController
@RequestMapping("api/accessControlVideo")
public class AccessControlVideoController extends BaseController {

    @Autowired
    private AccessControlVideoService accessControlVideoService;

    /**
     * 根据门禁ID查询关联的视频列表
     */
    @GetMapping("videos/{accessControlId}")
    public List<VideoEntity> getVideosByAccessControlId(@PathVariable String accessControlId) {
        return accessControlVideoService.findVideosByAccessControlId(accessControlId);
    }

    /**
     * 根据视频ID查询关联的门禁列表
     */
    @GetMapping("accessControls/{videoId}")
    public List<AccessControlVideo> getAccessControlsByVideoId(@PathVariable String videoId) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return accessControlVideoService.findByVideoId(videoId, tenantId);
    }

    /**
     * 批量保存门禁视频关联
     */
    @PostMapping("batch")
    public List<AccessControlVideo> batchSave(@RequestBody BatchSaveRequest request) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return accessControlVideoService.batchSave(request.getAccessControlId(), request.getVideoIds(), tenantId);
    }

    /**
     * 保存单个门禁视频关联
     */
    @PostMapping
    public AccessControlVideo save(@RequestBody AccessControlVideo entity) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        entity.setTenantId(tenantId);
        return accessControlVideoService.save(entity);
    }

    /**
     * 删除门禁视频关联
     */
    @DeleteMapping("{id}")
    public boolean delete(@PathVariable String id) {
        return accessControlVideoService.delete(id);
    }

    /**
     * 根据门禁ID删除所有关联
     */
    @DeleteMapping("byAccessControl/{accessControlId}")
    public boolean deleteByAccessControlId(@PathVariable String accessControlId) {
        return accessControlVideoService.deleteByAccessControlId(accessControlId);
    }

    /**
     * 根据视频ID删除所有关联
     */
    @DeleteMapping("byVideo/{videoId}")
    public boolean deleteByVideoId(@PathVariable String videoId) {
        return accessControlVideoService.deleteByVideoId(videoId);
    }

    /**
     * 检查门禁和视频是否已关联
     */
    @GetMapping("check/{accessControlId}/{videoId}")
    public boolean isAssociated(@PathVariable String accessControlId, @PathVariable String videoId) {
        return accessControlVideoService.isAssociated(accessControlId, videoId);
    }

    /**
     * 获取门禁关联的视频数量
     */
    @GetMapping("count/videos/{accessControlId}")
    public int getVideoCount(@PathVariable String accessControlId) {
        return accessControlVideoService.getVideoCountByAccessControlId(accessControlId);
    }

    /**
     * 获取视频关联的门禁数量
     */
    @GetMapping("count/accessControls/{videoId}")
    public int getAccessControlCount(@PathVariable String videoId) {
        return accessControlVideoService.getAccessControlCountByVideoId(videoId);
    }

    /**
     * 根据门禁ID查询关联信息
     */
    @GetMapping("relations/{accessControlId}")
    public List<AccessControlVideo> getRelationsByAccessControlId(@PathVariable String accessControlId) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return accessControlVideoService.findByAccessControlId(accessControlId, tenantId);
    }

    /**
     * 批量保存请求对象
     */
    public static class BatchSaveRequest {
        private String accessControlId;
        private List<String> videoIds;

        public String getAccessControlId() {
            return accessControlId;
        }

        public void setAccessControlId(String accessControlId) {
            this.accessControlId = accessControlId;
        }

        public List<String> getVideoIds() {
            return videoIds;
        }

        public void setVideoIds(List<String> videoIds) {
            this.videoIds = videoIds;
        }

        @Override
        public String toString() {
            return "BatchSaveRequest{" +
                    "accessControlId='" + accessControlId + '\'' +
                    ", videoIds=" + videoIds +
                    '}';
        }
    }
}
