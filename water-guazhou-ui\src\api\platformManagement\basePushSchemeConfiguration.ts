import { request } from '@/plugins/axios'

// 获取推送方案配置列表
export function getPushSchemeConfigList(params: any) {
  return request({
    url: '/api/base/push/scheme/configuration/list',
    method: 'get',
    params
  })
}

// 获取推送方案配置详情
export function getPushSchemeConfigDetail(id: string) {
  return request({
    url: '/api/base/push/scheme/configuration/getDetail',
    method: 'get',
    params: { id }
  })
}

// 新增推送方案配置配置
export function addPushSchemeConfig(data: any) {
  return request({
    url: '/api/base/push/scheme/configuration/add',
    method: 'post',
    data
  })
}

// 修改推送方案配置配置
export function editPushSchemeConfig(data: any) {
  return request({
    url: '/api/base/push/scheme/configuration/edit',
    method: 'post',
    data
  })
}

// 删除推送方案配置配置
export function deletePushSchemeConfig(ids: string[]) {
  return request({
    url: '/api/base/push/scheme/configuration/deleteIds',
    method: 'delete',
    data: ids
  })
} 