package org.thingsboard.server.controller.device;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.device.DeviceModelInfoService;
import org.thingsboard.server.dao.model.sql.DeviceModelInfoEntity;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

@RestController
@RequestMapping({"api/device/modelInfo"})
public class DeviceModelInfoController extends BaseController {
    @Autowired
    private DeviceModelInfoService deviceModelInfoService;

    public DeviceModelInfoController() {
    }

    @GetMapping({"list"})
    public IstarResponse getList(DeviceModelInfoEntity deviceModelInfoEntity, int page, int size) {
        return IstarResponse.ok(this.deviceModelInfoService.getList(deviceModelInfoEntity, page, size));
    }

    @PostMapping({"save"})
    public IstarResponse save(@RequestBody DeviceModelInfoEntity deviceModelInfoEntity) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(this.getTenantId().getId());
        deviceModelInfoEntity.setTenantId(tenantId);
        return IstarResponse.ok(this.deviceModelInfoService.save(deviceModelInfoEntity));
    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> ids) {
        return this.deviceModelInfoService.delete(ids);
    }

    @GetMapping({"getByDeviceId/{deviceId}"})
    public DeviceModelInfoEntity getByDeviceId(@PathVariable String deviceId) {
        return this.deviceModelInfoService.getByDeviceId(deviceId);
    }
}
