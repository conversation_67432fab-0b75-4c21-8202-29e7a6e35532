<template>
  <el-dialog
    v-model="visible"
    title="事件审核"
    width="1000px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="review-content">
      <!-- 事件信息展示 - 使用EventOverviewForm的查看模式 -->
      <div class="event-info-section">
        <h4 style="margin-bottom: 16px; color: #303133; font-weight: 600;">事件信息</h4>
        <div class="event-info-content">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">事件类型：</span>
                <span class="info-value">{{ eventFormData.typeName || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">事件名称：</span>
                <span class="info-value">{{ eventFormData.title || '-' }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">创建时间：</span>
                <span class="info-value">{{ eventFormData.createTime || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">事件地址：</span>
                <span class="info-value">{{ eventFormData.address || '-' }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <div class="info-item">
                <span class="info-label">事件描述：</span>
                <span class="info-value">{{ eventFormData.remark || '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 审核部分 -->
      <div class="review-section">
        <h4 style="margin-bottom: 16px; color: #303133; font-weight: 600;">审核意见</h4>
        <el-form :model="reviewForm" label-width="100px" class="review-form">
          <!-- 审核意见选择 -->
          <el-form-item label="审核意见" required>
            <el-radio-group v-model="reviewForm.reviewType" @change="handleReviewTypeChange">
              <el-radio value="approve">审核通过</el-radio>
              <el-radio value="reject">驳回</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 审核通过时显示是否办结选择 -->
          <el-form-item v-if="reviewForm.reviewType === 'approve'" label="是否办结" required>
            <el-radio-group v-model="reviewForm.handleType" @change="handleHandleTypeChange">
              <el-radio value="direct">直接办结</el-radio>
              <el-radio value="transfer">转工单</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 选择转工单时显示人员选择 -->
          <el-form-item
            v-if="reviewForm.reviewType === 'approve' && reviewForm.handleType === 'transfer'"
            label="处理人员"
            required
          >
            <el-select
              v-model="reviewForm.assignUser"
              placeholder="请选择处理人员"
              style="width: 240px"
              filterable
              clearable
            >
              <el-option
                v-for="user in userOptions"
                :key="user.value"
                :label="user.label"
                :value="user.value"
              />
            </el-select>
          </el-form-item>

          <!-- 驳回时显示原因输入 -->
          <el-form-item v-if="reviewForm.reviewType === 'reject'" label="驳回原因" required>
            <el-input
              v-model="reviewForm.rejectReason"
              type="textarea"
              placeholder="请输入驳回原因"
              :rows="3"
              style="width: 400px"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          提交
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, onMounted } from 'vue'
import { CompleteEventOverview, ApproveEventOverview, RejectEventOverview } from '@/api/workorder/eventOverview'
import { SLMessage } from '@/utils/Message'
import useUser from '@/hooks/user/useUser'
import { removeSlash } from '@/utils/removeIdSlash'

interface ReviewForm {
  reviewType: 'approve' | 'reject' | ''
  handleType: 'direct' | 'transfer' | ''
  rejectReason: string
  assignUser: string
}

const props = defineProps<{
  modelValue: boolean
  eventData: any
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'submit': []
}>()

const visible = ref(false)
const submitting = ref(false)

const reviewForm = reactive<ReviewForm>({
  reviewType: '',
  handleType: '',
  rejectReason: '',
  assignUser: ''
})

// 用户选择相关
const { getUserOptions } = useUser()
const userOptions = ref<any[]>([])

// EventOverviewForm相关变量
const eventFormData = computed(() => {
  if (!props.eventData) return {
    typeCategory: [],
    title: '',
    type: '',
    createTime: '',
    address: '',
    remark: ''
  }

  // 转换事件数据格式以适配EventOverviewForm
  return {
    id: props.eventData.id || '',
    title: props.eventData.title || '',
    type: props.eventData.type || '',
    typeName: props.eventData.typeName || '',
    typeCategory: props.eventData.type ? [props.eventData.type] : [],
    address: props.eventData.address || '',
    remark: props.eventData.remark || '',
    coordinate: props.eventData.coordinate || '',
    coordinateName: props.eventData.coordinateName || '',
    createTime: props.eventData.createTime || '',
    status: props.eventData.status || '',
    rejectReason: props.eventData.rejectReason || ''
  }
})

// 监听 modelValue 变化
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    resetForm()
  }
})

// 监听 visible 变化
watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 重置表单
const resetForm = () => {
  reviewForm.reviewType = ''
  reviewForm.handleType = ''
  reviewForm.rejectReason = ''
  reviewForm.assignUser = ''
}

// 审核类型变化处理
const handleReviewTypeChange = () => {
  reviewForm.handleType = ''
  reviewForm.rejectReason = ''
  reviewForm.assignUser = ''
}

// 处理方式变化处理
const handleHandleTypeChange = () => {
  reviewForm.assignUser = ''
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}

// 提交审核
const handleSubmit = async () => {
  if (!reviewForm.reviewType) {
    SLMessage.warning('请选择审核意见')
    return
  }

  if (reviewForm.reviewType === 'approve' && !reviewForm.handleType) {
    SLMessage.warning('请选择是否办结')
    return
  }

  if (reviewForm.reviewType === 'approve' && reviewForm.handleType === 'transfer' && !reviewForm.assignUser) {
    SLMessage.warning('请选择处理人员')
    return
  }

  if (reviewForm.reviewType === 'reject' && !reviewForm.rejectReason.trim()) {
    SLMessage.warning('请输入驳回原因')
    return
  }

  try {
    submitting.value = true

    if (reviewForm.reviewType === 'approve') {
      // 审核通过
      if (reviewForm.handleType === 'direct') {
        // 直接办结
        await CompleteEventOverview(props.eventData.id)
        SLMessage.success('审核通过，事件已办结')
      } else {
        // 转工单
        await ApproveEventOverview(props.eventData.id, { assignUserId: reviewForm.assignUser })
        SLMessage.success('审核通过，已转工单')
      }
    } else {
      // 驳回
      await RejectEventOverview(props.eventData.id, { reason: reviewForm.rejectReason })
      SLMessage.success('事件已驳回')
    }

    handleClose()
    emit('submit')
  } catch (error) {
    console.error('审核失败:', error)
    SLMessage.error('审核失败')
  } finally {
    submitting.value = false
  }
}

// 初始化用户选项
const initUserOptions = async () => {
  try {
    const options = await getUserOptions(false, { authority: 'CUSTOMER_USER' })

    // 处理用户ID，去除连字符以保持与NewOrder.vue一致
    userOptions.value = options.map(option => ({
      ...option,
      value: removeSlash(option.value)
    }))
  } catch (error) {
    console.error('获取用户列表失败:', error)
  }
}

// 组件挂载时初始化
onMounted(() => {
  initUserOptions()
})
</script>

<style scoped>
.review-content {
  padding: 0;
}

.event-info-section {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.event-info-content {
  margin-top: 16px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  min-height: 32px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
  margin-right: 12px;
  flex-shrink: 0;
  line-height: 32px;
}

.info-value {
  color: #303133;
  flex: 1;
  line-height: 32px;
  word-break: break-all;
}

.reject-reason {
  color: #f56c6c;
  font-weight: 500;
}

.event-info {
  margin-bottom: 30px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.info-row {
  display: flex;
  margin-bottom: 10px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  width: 80px;
  color: #666;
  flex-shrink: 0;
}

.value {
  color: #333;
  flex: 1;
}

.review-section {
  padding: 20px;
  background-color: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.review-form {
  margin-top: 16px;
}

.review-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

.review-form :deep(.el-radio-group) {
  display: flex;
  gap: 16px;
}

.review-form :deep(.el-radio) {
  margin-right: 0;
}

.review-form :deep(.el-form-item) {
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
}
</style>
