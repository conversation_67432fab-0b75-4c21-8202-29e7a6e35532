package org.thingsboard.server.dao.util.imodel.query.deviceIcon;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.deviceIcon.DeviceIcon;
import org.thingsboard.server.dao.util.imodel.query.PageableQueryEntity;

import java.util.Date;

/**
 * 设备图标分页查询请求
 */
@Getter
@Setter
public class DeviceIconPageRequest extends PageableQueryEntity<DeviceIcon> {
    /**
     * 设备类型
     */
    private String deviceType;


    /**
     * 设备状态
     */
    private String deviceStatus;

    /**
     * 租户ID
     */
    private String tenantId;
}
