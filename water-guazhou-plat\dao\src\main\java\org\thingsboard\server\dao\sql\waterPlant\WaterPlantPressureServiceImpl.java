package org.thingsboard.server.dao.sql.waterPlant;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.waterPlant.WaterPlantPressure;
import org.thingsboard.server.dao.util.SqlDao;
import org.thingsboard.server.dao.util.imodel.query.waterPlant.WaterPlantPressurePageRequest;
import org.thingsboard.server.dao.util.imodel.query.waterPlant.WaterPlantPressureSaveRequest;
import org.thingsboard.server.dao.waterPlant.WaterPlantPressureService;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 水厂压力信息服务实现类
 */
@Service
@SqlDao
public class WaterPlantPressureServiceImpl implements WaterPlantPressureService {

    @Autowired
    private WaterPlantPressureMapper waterPlantPressureMapper;

    @Override
    public IPage<WaterPlantPressure> findByPage(WaterPlantPressurePageRequest request) {
        return waterPlantPressureMapper.findByPage(request);
    }

    @Override
    public WaterPlantPressure saveOrUpdate(WaterPlantPressureSaveRequest request, TenantId tenantId) {
        WaterPlantPressure entity = new WaterPlantPressure();
        BeanUtils.copyProperties(request, entity);

        if (StringUtils.isNotBlank(request.getId())) {
            // 有ID，执行更新
            waterPlantPressureMapper.updateById(entity);
        } else {
            // 没有ID，执行新增
            entity.setId(UUID.randomUUID().toString());
            entity.setCreateTime(new Date());
            entity.setTenantId(tenantId.toString());
            entity.setDataSource("手动填报");
            entity.setCreator(tenantId.toString()); // 实际应用中应该是当前用户ID
            waterPlantPressureMapper.insert(entity);
        }

        return entity;
    }

    private String getExistingId(String waterPlantId, Date recordTime, String tenantId) {
        // 查询已存在记录的ID
        QueryWrapper<WaterPlantPressure> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("water_plant_id", waterPlantId)
                   .apply("DATE(record_time) = DATE({0})", new SimpleDateFormat("yyyy-MM-dd").format(recordTime))
                   .eq("tenant_id", tenantId);
        WaterPlantPressure existingRecord = waterPlantPressureMapper.selectOne(queryWrapper);
        return existingRecord != null ? existingRecord.getId() : null;
    }

    @Override
    public void batchImport(List<WaterPlantPressureSaveRequest> list, TenantId tenantId) {
        if (list == null || list.isEmpty()) {
            return;
        }

        List<WaterPlantPressure> entityList = new ArrayList<>();
        for (WaterPlantPressureSaveRequest request : list) {
            WaterPlantPressure entity = new WaterPlantPressure();
            BeanUtils.copyProperties(request, entity);
            entity.setId(UUID.randomUUID().toString());
            entity.setCreateTime(new Date());
            entity.setTenantId(tenantId.toString());
            entity.setDataSource("导入");
            entity.setCreator(tenantId.toString()); // 实际应用中应该是当前用户ID
            entityList.add(entity);
        }

        waterPlantPressureMapper.batchInsert(entityList);
    }

    @Override
    public List<WaterPlantPressure> exportList(WaterPlantPressurePageRequest request) {
        return waterPlantPressureMapper.exportList(request);
    }

    @Override
    public boolean delete(String id) {
        return waterPlantPressureMapper.deleteById(id) > 0;
    }
}
