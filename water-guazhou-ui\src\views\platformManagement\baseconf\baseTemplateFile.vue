<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      class="card-table"
      :config="TableConfig"
    ></CardTable>
    <DialogForm
      ref="refDialogForm"
      :config="DialogFormConfig"
    ></DialogForm>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue'
import { 
  getTemplateFileList, 
  addTemplateFile, 
  editTemplateFile, 
  deleteTemplateFile,
  getTemplateFileDetail 
} from '@/api/platformManagement/baseTemplateFile'
import { SLConfirm, SLMessage } from '@/utils/Message'

const refSearch = ref()
const refDialogForm = ref()

const SearchConfig = reactive({
  labelWidth: '100px',
  filters: [
    { 
      type: 'input', 
      label: '模板名称', 
      field: 'name', 
      placeholder: '请输入模板名称',
      onChange: () => refreshData() 
    },
    {
      type: 'btn-group',
      btns: [
        { type: 'primary', perm: true, text: '查询', click: () => refreshData() },
        { perm: true, type: 'primary', text: '新增', click: () => handleAdd() },
        { perm: true, type: 'danger', text: '批量删除', click: () => handleDelete() }
      ]
    }
  ],
  defaultParams: {}
})

const TableConfig = reactive({
  columns: [
    { label: '模板名称', prop: 'name' },
    { label: '模板编码', prop: 'code' },
    { label: '分类', prop: 'type' },
    { label: '文件类型', prop: 'fileType' },
    { label: '文件路径', prop: 'filePath' },
    { label: '描述', prop: 'description' }
  ],
  dataList: [],
  operations: [
    {
      perm: true,
      type: 'primary',
      isTextBtn: true,
      text: '查看详情',
      click: (row) => handleDetail(row)
    },
    {
      perm: true,
      type: 'primary',
      isTextBtn: true,
      text: '编辑',
      click: (row) => handleAdd(row)
    },
    {
      perm: true,
      type: 'danger',
      isTextBtn: true,
      text: '删除',
      click: (row) => handleDelete(row)
    }
  ],
  pagination: {
    total: 0,
    page: 1,
    align: 'right',
    limit: 20,
    handlePage: (page) => {
      TableConfig.pagination.page = page
      refreshData()
    },
    handleSize: (size) => {
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  handleSelectChange: (rows) => {
    TableConfig.selectList = rows || []
  }
})

const DialogFormConfig = reactive({
  title: '新增模板文件',
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '模板名称',
          field: 'name',
          rules: [{ required: true, message: '请输入模板名称' }]
        },
        {
          type: 'input',
          label: '模板编码',
          field: 'code',
          rules: [{ required: true, message: '请输入模板编码' }]
        },
        {
          type: 'input',
          label: '分类',
          field: 'type',
          rules: [{ required: true, message: '请输入分类' }]
        },
        {
          type: 'input',
          label: '文件类型',
          field: 'fileType',
          rules: [{ required: true, message: '请输入文件类型' }]
        },
        {
          type: 'input',
          label: '文件路径',
          field: 'filePath',
          rules: [{ required: true, message: '请输入文件路径' }]
        },
        {
          type: 'textarea',
          label: '描述',
          field: 'description',
          placeholder: '请输入描述信息'
        }
      ]
    }
  ],
  labelPosition: 'top',
  defaultValue: {},
  dialogWidth: 600,
  draggable: true,
  showSubmit: true,
  showCancel: true,
  cancelText: '取消',
  submitText: '确定',
  submit: async (params) => {
    try {
      if (params.id) {
        await editTemplateFile(params)
        SLMessage.success('修改成功')
      } else {
        await addTemplateFile(params)
        SLMessage.success('新增成功')
      }
      refDialogForm.value?.closeDialog()
      refreshData()
    } catch (error) {
      SLMessage.error('操作失败')
    }
  }
})

// 重置对话框配置
const resetDialogConfig = () => {
  DialogFormConfig.group[0].fields.forEach(field => {
    field.disabled = false
    field.readonly = false
  })
  
  DialogFormConfig.showSubmit = true
  DialogFormConfig.showCancel = true
  DialogFormConfig.cancelText = '取消'
  DialogFormConfig.submitText = '确定'
  
  DialogFormConfig.submit = async (params) => {
    try {
      if (params.id) {
        await editTemplateFile(params)
        SLMessage.success('修改成功')
      } else {
        await addTemplateFile(params)
        SLMessage.success('新增成功')
      }
      refDialogForm.value?.closeDialog()
      refreshData()
    } catch (error) {
      SLMessage.error('操作失败')
    }
  }
}

// 查看详情
const handleDetail = async (row) => {
  try {
    const res = await getTemplateFileDetail(row.id)
    const detailData = res.data?.data || res
    
    resetDialogConfig()
    DialogFormConfig.title = '变更记录详情'
    DialogFormConfig.defaultValue = { ...detailData }
    DialogFormConfig.group[0].fields.forEach(field => {
      field.disabled = true
    })
    DialogFormConfig.showSubmit = false
    DialogFormConfig.cancelText = '关闭'
    refDialogForm.value?.openDialog()
  } catch (error) {
    SLMessage.error('获取详情失败')
  }
}

// 新增/编辑
const handleAdd = (row) => {
  resetDialogConfig()
  
  if (row) {
    DialogFormConfig.title = '编辑模板文件'
    DialogFormConfig.defaultValue = { ...row }
  } else {
    DialogFormConfig.title = '新增模板文件'
    DialogFormConfig.defaultValue = {}
  }
  
  refDialogForm.value?.openDialog()
}

// 删除
const handleDelete = async (row) => {
  try {
    const ids = row ? [row.id] : TableConfig.selectList.map(item => item.id)
    if (!ids.length) {
      SLMessage.warning('请选择要删除的数据')
      return
    }
    
    await SLConfirm('确定要删除选中的数据吗？')
    await deleteTemplateFile(ids)
    SLMessage.success('删除成功')
    refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      SLMessage.error('删除失败')
    }
  }
}

// 刷新数据
const refreshData = async () => {
  try {
    const res = await getTemplateFileList({
      page: TableConfig.pagination.page,
      size: TableConfig.pagination.limit,
      ...(refSearch.value?.queryParams || {})
    })
    const responseData = res.data?.data || res
    TableConfig.dataList = responseData.records || responseData
    TableConfig.pagination.total = responseData.total || responseData.length || 0
  } catch (error) {
    SLMessage.error('数据加载失败')
  }
}

onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.wrapper {
  padding: 20px;
}

.card-table {
  margin-top: 20px;
}
</style>