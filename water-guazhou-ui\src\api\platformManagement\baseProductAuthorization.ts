import { request } from '@/plugins/axios'

// 获取产品授权列表
export function getBaseProductAuthorizationList(params: any) {
  return request({
    url: '/api/base/product/authorization/list',
    method: 'get',
    params
  })
}

// 获取产品授权详情
export function getBaseProductAuthorizationDetail(id: string) {
  return request({
    url: '/api/base/product/authorization/getDetail',
    method: 'get',
    params: { id }
  })
}

// 新增产品授权配置
export function addBaseProductAuthorization(data: any) {
  return request({
    url: '/api/base/product/authorization/add',
    method: 'post',
    data
  })
}

// 修改产品授权配置
export function editBaseProductAuthorization(data: any) {
  return request({
    url: '/api/base/product/authorization/edit',
    method: 'post',
    data
  })
}

// 删除产品授权配置
export function deleteBaseProductAuthorization(ids: string[]) {
  return request({
    url: '/api/base/product/authorization/deleteIds',
    method: 'delete',
    data: ids
  })
} 