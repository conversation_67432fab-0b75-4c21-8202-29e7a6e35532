import { request } from '@/plugins/axios'

// 获取数据库连接列表
export function getDatabaseConnectionList(params: any) {
  return request({
    url: '/api/base/database/connection/list',
    method: 'get',
    params
  })
}

// 获取数据库连接详情
export function getDatabaseConnectionDetail(id: string) {
  return request({
    url: '/api/base/database/connection/getDetail',
    method: 'get',
    params: { id }
  })
}

// 新增数据库连接配置
export function addDatabaseConnection(data: any) {
  return request({
    url: '/api/base/database/connection/add',
    method: 'post',
    data
  })
}

// 修改数据库连接配置
export function editDatabaseConnection(data: any) {
  return request({
    url: '/api/base/database/connection/edit',
    method: 'post',
    data
  })
}

// 删除数据库连接配置
export function deleteDatabaseConnection(ids: string[]) {
  return request({
    url: '/api/base/database/connection/deleteIds',
    method: 'delete',
    data: ids
  })
} 