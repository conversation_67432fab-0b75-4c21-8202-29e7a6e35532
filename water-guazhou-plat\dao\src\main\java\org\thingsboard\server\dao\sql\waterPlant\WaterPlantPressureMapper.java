package org.thingsboard.server.dao.sql.waterPlant;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.waterPlant.WaterPlantPressure;
import org.thingsboard.server.dao.util.imodel.query.waterPlant.WaterPlantPressurePageRequest;

import java.util.Date;
import java.util.List;

/**
 * 水厂压力信息Mapper接口
 */
@Mapper
public interface WaterPlantPressureMapper extends BaseMapper<WaterPlantPressure> {
    
    /**
     * 分页查询水厂压力信息
     */
    IPage<WaterPlantPressure> findByPage(WaterPlantPressurePageRequest request);
    
    /**
     * 检查指定日期的水厂压力记录是否存在
     */
    Integer checkExists(@Param("waterPlantId") String waterPlantId, 
                       @Param("recordTime") Date recordTime, 
                       @Param("tenantId") String tenantId);
    
    /**
     * 批量导入水厂压力信息
     */
    void batchInsert(@Param("list") List<WaterPlantPressure> list);
    
    /**
     * 导出水厂压力信息
     */
    List<WaterPlantPressure> exportList(WaterPlantPressurePageRequest request);
}
