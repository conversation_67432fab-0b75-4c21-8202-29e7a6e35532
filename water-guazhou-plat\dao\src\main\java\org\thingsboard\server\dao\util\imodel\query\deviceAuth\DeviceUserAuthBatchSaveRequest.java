package org.thingsboard.server.dao.util.imodel.query.deviceAuth;

import lombok.Data;

import java.util.List;

/**
 * 设备用户权限关联批量保存请求
 */
@Data
public class DeviceUserAuthBatchSaveRequest {
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 设备名称
     */
    private String deviceName;
    
    /**
     * 设备编码
     */
    private String deviceSerial;
    
    /**
     * 用户权限列表
     */
    private List<UserAuthItem> userAuthList;
    
    /**
     * 用户权限项
     */
    @Data
    public static class UserAuthItem {
        /**
         * 用户ID
         */
        private String userId;
        
        /**
         * 用户名称
         */
        private String userName;
        
        /**
         * 权限类型（1:查看,2:控制,3:管理）
         */
        private Integer authType;
    }
}
